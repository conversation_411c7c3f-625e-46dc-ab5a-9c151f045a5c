## 🎯 开发目标

简要说明你要生成的页面/组件/功能目标。

## 🧱 技术栈要求

- 框架：React@18.x / Vue@3.x
- 组件库：Ant Design / Vant / Element UI 等
- 样式库：Tailwind CSS、less、sass 等
- 图表库（如有）：Ant Design Charts / ECharts / Chart.js
- 类型安全：TypeScript @4.x
- 单元测试：Jest / Viest
- 文档注释：jsdoc 规范
- 其他要求（如 hooks / ts 支持 / 无类组件）

## 📋 代码规范检查

- [ ] 命名规范：类名、方法名、变量名符合项目规范
- [ ] 异常处理：使用统一的 try catch 和错误码
- [ ] 日志记录：关键操作添加适当的日志
- [ ] 参数验证：外部输入进行必要的验证
- [ ] 接口响应：处理返回格式符合项目规范

## 🧩 页面/组件功能描述

### 1. 功能清单

- [ ] 功能 A：xx 列表，支持分页、搜索
- [ ] 功能 B：支持新增、编辑、删除
- [ ] 功能 C：接口联动、表单校验

### 2. 页面结构

可用列表描述结构块：

- 顶部搜索区：输入框 + 下拉框
- 中部表格区：字段、操作列、分页
- 底部：按钮或统计信息等

## 🧮 接口与数据结构

### 1. 接口列表

- GET `/api/items`：获取列表数据，参数为 `page`, `size`, `keyword`
- POST `/api/items`：新增数据
- PUT `/api/items/{id}`：修改
- DELETE `/api/items/{id}`：删除

### 2. 数据字段说明

| 字段     | 类型   | 描述     | 是否必填 |
| -------- | ------ | -------- | -------- |
| id       | number | 主键 ID  | 否       |
| name     | string | 商品名称 | 是       |
| category | string | 分类     | 是       |

## 🧩 特殊交互/边界逻辑（如有）

- 删除前弹出确认框
- 表单提交失败时高亮错误项
- 请求异常时展示 message.error

## 📌 输出要求

- 单文件组件结构 / 拆分结构（根据项目约定）
- 代码需可直接运行（无需额外配置）
- 使用 hook（如 useEffect / useState）管理逻辑
- 类型语言：JavaScript / TypeScript（如需指定）
- 遵守代码简约之道

## 🔄 影响范围

### 向前兼容性

- [ ] 不影响现有页面、组件功能
- [ ] 不影响现有数据结构

### 依赖关系

- **上游依赖**: \[依赖的其他模块或服务\]
- **下游影响**: \[可能影响的其他模块或客户端\]

## 📝 示例/补充说明（可选）

可插入界面草图（Figma 链接）、表格示意图或已有页面链接作为参考。

**注意：本模版会根据项目实践不断更新优化，请关注版本变更。**
