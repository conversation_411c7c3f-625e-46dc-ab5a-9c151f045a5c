# 擎路 H5 项目架构文档

## 项目概述

擎路 H5 是一个基于 Vue 3 和 Vite 构建的移动端 H5 应用，主要用于车辆相关业务的管理和操作。项目采用现代前端技术栈，具有良好的开发体验和性能表现。

## 技术栈

### 核心技术

- **Vue 3** - 前端框架，使用 Composition API
- **Vite** - 构建工具，提供快速的开发体验
- **Vue Router 4** - 单页面应用路由管理
- **Vant 3** - 移动端 UI 组件库
- **Element Plus** - 桌面端 UI 组件库（部分场景使用）

### 开发工具

- **TypeScript** - 提供类型支持
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **Vitest** - 单元测试框架

### 样式解决方案

- **Tailwind CSS** - 原子化 CSS 框架
- **Less** - CSS 预处理器
- **PostCSS** - CSS 后处理器
- **amfe-flexible** - 移动端适配方案
- **postcss-pxtorem** - px 转 rem 工具

### 工具库

- **Axios** - HTTP 客户端
- **Lodash-es** - 工具函数库
- **Day.js** - 日期处理库
- **Moment.js** - 日期处理库
- **Nanoid** - ID 生成器
- **Animate.css** - CSS 动画库

## 项目目录结构

```
qinglu-h5/
├── .cursor/                    # Cursor编辑器配置
├── .git/                       # Git版本控制
├── .vscode/                    # VS Code配置
├── __tests__/                  # 测试文件目录
├── mock/                       # Mock数据
│   ├── source/                 # Mock数据源
│   └── utils/                  # Mock工具函数
├── node_modules/               # 依赖包
├── src/                        # 源代码目录
│   ├── components/             # 公共组件
│   │   ├── business/           # 业务组件
│   │   ├── form/               # 表单组件
│   │   ├── roles/              # 角色组件
│   │   └── index.js            # 组件导出
│   ├── constants/              # 常量定义
│   ├── services/               # 服务层
│   │   ├── apis/               # API接口
│   │   ├── entities/           # 实体处理
│   │   ├── models/             # 数据模型
│   │   ├── http.config.js      # HTTP配置
│   │   └── README.md           # 服务层文档
│   ├── theme/                  # 主题样式
│   ├── use/                    # 组合式API
│   ├── utils/                  # 工具函数
│   ├── views/                  # 页面视图
│   │   ├── account/            # 账户管理
│   │   ├── agreement/          # 协议相关
│   │   ├── auth/               # 认证相关
│   │   ├── channelManage/      # 渠道管理
│   │   ├── damage/             # 损坏管理
│   │   ├── dataOverview/       # 数据概览
│   │   ├── demo/               # 演示页面
│   │   ├── illegal/            # 违法管理
│   │   ├── illegal-manage/     # 违法管理
│   │   ├── index/              # 首页
│   │   ├── inventory/          # 库存管理
│   │   ├── long-order/         # 长订单
│   │   ├── order/              # 订单管理
│   │   ├── person/             # 个人中心
│   │   ├── price/              # 价格管理
│   │   ├── renewal/            # 续约管理
│   │   ├── stock/              # 库存
│   │   ├── tickets/            # 票务管理
│   │   ├── vehicleControl/     # 车辆控制
│   │   ├── vehicleControlDetails/ # 车辆控制详情
│   │   ├── wukongLogin/        # 悟空登录
│   │   └── wukongLoginCode/    # 悟空登录验证码
│   ├── App.vue                 # 根组件
│   ├── main.js                 # 应用入口
│   ├── router.js               # 路由配置
│   ├── setupTests.js           # 测试配置
│   └── tailwind.css            # Tailwind CSS入口
├── .gitignore                  # Git忽略文件
├── .prettierrc.cjs             # Prettier配置
├── eslint.config.js            # ESLint配置
├── index.html                  # HTML模板
├── package.json                # 项目配置
├── package-lock.json           # 依赖锁定文件
├── pnpm-lock.yaml              # PNPM锁定文件
├── postcss.config.cjs          # PostCSS配置
├── README.md                   # 项目说明
├── tailwind.config.cjs         # Tailwind CSS配置
├── vite.config.js              # Vite配置
└── vitest.config.js            # Vitest测试配置
```

## 核心架构设计

### 1. 分层架构

项目采用经典的分层架构模式：

- **视图层(Views)**: 负责页面渲染和用户交互
- **组件层(Components)**: 可复用的 UI 组件
- **服务层(Services)**: 数据处理和 API 调用
- **工具层(Utils)**: 通用工具函数
- **常量层(Constants)**: 应用常量定义

### 2. 服务层架构

服务层采用三层结构：

- **APIs**: 远程接口调用和 Model 格式化处理
- **Entities**: 统一处理数据的调用和格式化
- **Models**: 数据模型，负责数据格式化和扩展

### 3. 组件架构

组件按功能分类：

- **业务组件(business)**: 特定业务逻辑的组件
- **表单组件(form)**: 表单相关的通用组件
- **角色组件(roles)**: 角色权限相关组件
- **通用组件**: 基础 UI 组件

### 4. 状态管理

项目使用 Vue 3 的 Composition API 进行状态管理：

- **useFormCache**: 表单缓存管理
- **useOrderEnums**: 订单枚举管理
- **usePageLoading**: 页面加载状态管理
- **useRouteSteps**: 路由步骤管理
- **useThemeVars**: 主题变量管理
- **useUpdateSystem**: 系统更新管理

## 开发环境配置

### 环境变量支持

项目支持多环境配置：

- `dev`: 开发环境
- `dev2`: 第二开发环境
- `alter1/alter2/alter3`: 备用环境
- `prod`: 生产环境
- `hello/ultra/bugfix/beyond/stable`: 特定环境

### 构建配置

- 使用 Vite 作为构建工具
- 支持热更新和快速构建
- 集成 Mock 服务用于开发调试
- 支持多环境构建和部署

### 代码规范

- ESLint 配置确保代码质量
- Prettier 配置统一代码格式
- TypeScript 提供类型检查
- 单元测试确保代码可靠性

## 移动端适配

### 响应式设计

- 使用`amfe-flexible`进行移动端适配
- `postcss-pxtorem`自动转换 px 为 rem
- Tailwind CSS 提供响应式工具类

### UI 组件

- 主要使用 Vant 3 作为移动端 UI 组件库
- Element Plus 作为补充 UI 组件库
- 自定义组件满足特定需求

## 性能优化

### 构建优化

- Vite 提供快速的开发和构建体验
- 自动代码分割和懒加载
- 资源压缩和优化

### 运行时优化

- Vue 3 的 Composition API 提供更好的性能
- 组件懒加载减少初始包大小
- 图片压缩和优化

## 部署和发布

### 构建命令

- `npm run build`: 生产环境构建
- `npm run build:preview`: 预览环境构建
- `npm run preview`: 预览模式

### 环境配置

- 支持多环境配置和部署
- 自动化构建和部署流程
- 资源 CDN 配置支持

## 测试策略

### 单元测试

- 使用 Vitest 作为测试框架
- 测试覆盖率报告
- Vue Test Utils 支持组件测试

### 开发测试

- Mock 数据支持本地开发测试
- VConsole 调试工具集成
- 多环境测试支持

## 开发规范

### 代码组织

- 按功能模块组织代码
- 统一的命名规范
- 清晰的文件结构

### API 设计

- 统一的 API 调用规范
- 数据模型标准化
- 错误处理机制

### 组件设计

- 组件复用性原则
- 清晰的组件职责
- 统一的组件接口

## 项目特色

### 业务特色

- 专注于车辆相关业务场景
- 完整的订单管理流程
- 多角色权限管理
- 数据可视化展示

### 技术特色

- 现代化的前端技术栈
- 完善的开发工具链
- 灵活的多环境配置
- 良好的代码组织结构

## 未来规划

### 技术升级

- 持续跟进 Vue 3 生态系统
- 性能监控和优化
- 移动端体验改进

### 功能扩展

- 更多业务场景支持
- 增强的数据分析能力
- 更好的用户体验

---

_文档版本: 1.0 最后更新: 2024 年维护者: 开发团队_
