```javascript
/**
 * 页面定义 - 用于导航菜单、面包屑和 react-router 路由
 *
 * - 顶层页面、页面分类、分类的直接子页面会显示在导航菜单
 * - 页面分类只出现在菜单，没有路由也没有页面内容
 * - 其他页面只会出现在面包屑
 */
import React from 'react'
import {
  HomeOutlined,
  SettingOutlined,
  FileTextOutlined,
  DollarOutlined,
  ReadOutlined,
} from '@ant-design/icons'

import OrderIcon from '@/assets/menu/order.svg'
import StockIcon from '@/assets/menu/stock.svg'
import PriceIcon from '@/assets/menu/price.svg'
import ReportIcon from '@/assets/menu/report.svg'
import StoreIcon from '@/assets/menu/store.svg'
import CarIcon from '@/assets/menu/car.svg'
import MerchantIcon from '@/assets/menu/merchant.svg'
import TaskIcon from '@/assets/menu/task.svg'
import BillIcon from '@/assets/menu/bill.svg'
import MarketIcon from '@/assets/menu/market.svg'
import ChannelIcon from '@/assets/menu/channel.svg'
import UserIcon from '@/assets/menu/user.svg'
import ProcureIcon from '@/assets/menu/procure.svg'

import { Dashboard } from '@/views/dashboard'
import { StoresModels } from '@/views/store/stores'
import { CancelRules } from '@/views/store/cancel-rules'
import { ManageServicePolicy } from '@/views/store/service-policy'
import { EditServicePolicy } from '@/views/store/service-policy/edit'
import { ServicePolicyDetail } from '@/views/store/service-policy/detail'
import { VehicleModels } from '@/views/vehicle/models'
import { EditVehicleModel } from '@/views/vehicle/edit-model'
import { VehicleModelDetail } from '@/views/vehicle/model-detail'
import { Vehicles } from '@/views/vehicle/vehicles'
import { EditVehicle } from '@/views/vehicle/edit-vehicle'
import { VehicleDetail } from '@/views/vehicle/vehicle-detail'
import { EditStore } from '@/views/store/edit-store'
import { StoreDetail } from '@/views/store/detail'
import { EditPickup } from '@/views/store/edit-pickup'
import { Accounts } from '@/views/account/accounts'
import { AccountDetail } from '@/views/account/detail'
import { EditAccount } from '@/views/account/edit'
import { Roles } from '@/views/account/roles'
import { Stock } from '@/views/stock'
import ChannelManage from '@/views/channel'
import OrderManage from '@/views/order'
import PriceList from '@/views/price/list'
import PriceDetail from '@/views/price/priceDetail'
import RentPrice from '@/views/price/rentPrice'
import AdditionalServicesList from '@/views/price/services'
import ServicePrice from '@/views/price/services/price'
import BatchEditServicePrice from '@/views/price/services/batch'
import InsuranceList from '@/views/price/insurance'
import InsurancePrice from '@/views/price/insurance/price'
import BatchEditInsurancePrice from '@/views/price/insurance/batch'
import CreditFreeByChannel from '@/views/price/creditFree'
import ChannelPackageSetup from '@/views/price/channelPackageSetup'
import XcLimitInquiry from '@/views/price/xcLimitInquiry'
import CreateOrder from '@/views/order/createOrder'
import OrderDetail from '@/views/order/orderDetail'
import OrderTaskManage from '@/views/order/taskManage'
import ReturnVehicle from '@/views/order/return'
import PickUpVehicle from '@/views/order/pickUp'
import Publicitys from '@/views/publicity/publicitys/index'
import CreatePublicitys from '@/views/publicity/publicitys/create'
import PandectView from '@/views/pandect/index'
import { PandectOverview } from '@/views/pandect/pandectOverview'
import { MerchantCertified } from '@/views/merchant/certified'
import { AccountNotify } from '@/views/account/notify'
import LongOrderManage from '@/views/long-order'
import LongOrderTaskManage from '@/views/long-order/taskManage'
import CreateLongOrder from '@/views/long-order/createOrder'
import LongOrderDetail from '@/views/long-order/orderDetail'
import LongReturnVehicle from '@/views/long-order/return'
import LongPickUpVehicle from '@/views/long-order/pickUp'
import { VehicleTickets } from '@/views/vehicle/ticket'
import { IllegalManage } from '@/views/vehicle/illegal'
import { IllegalDetail } from '@/views/vehicle/illegal/detail'
import Bill from '@/views/bill'
import DriverBoard from '@/views/driver-board'
import Alipay from '@/views/alipay'
import SystemInfo from '@/views/system-info'
import LogOperations from '@/views/log-operations'
import SystemInfoConfig from '@/views/system-info/config'
import { PriceOverview } from '@/views/price/overview'
import { Allopatry } from '@/views/store/allopatry'
import PurchasShop from '@/views/purchasShop'
import ElectronicContract from '@/views/electronic-contract'
import Agreement from '@/views/electronic-contract/agreement'
import ValidateCar from '@/views/electronic-contract/validateCar/index'
import ValidateCarView from '@/views/electronic-contract/validateCar/index-view'
import RentCar from '@/views/electronic-contract/rentCar/index'
import RentCatView from '@/views/electronic-contract/rentCar/index-view'
import AgreementView from '@/views/electronic-contract/agreement/index-view'
import PurchasList from '@/views/purchasShop/purchasList'
import PurchasListGPS from '@/views/purchasShop/purchasListGPS'
import VehicleControl from '@/views/vehicle/vehicleControl'
import ETCService from '@/views/vehicle/ETCService'
import GPSService from '@/views/purchasShop/purchasListGPS'
import FinanceRecharge from '@/views/finance'
import PaymentChannel from '@/views/finance/paymentChannel'
import AccountExtension from '@/views/finance/accountExtension'
import FinanceManagement from '@/views/financeManagement'
import FlowingRecords from '@/views/flowingRecords'
import FinancialOverview from '@/views/financialOverview'
import PullLimitData from '@/views/finance/pullLimitData'
import PullLimitPrice from '@/views/finance/pullLimitPrice'
import { EtcOrder } from '@/views/etc-manage/etc-order'
import { EtcOrderDetail } from '@/views/etc-manage/etc-order/detail'
import { EtcCar } from '@/views/etc-manage/etc-car'
import { EtcCarDetail } from '@/views/etc-manage/etc-car/detail'
import { PackageSetup } from '@/views/finance/packageSetup'
import { CodeGeneration } from '@/views/finance/codeGeneration'
import { EtcPublish } from '@/views/etc-manage/etc-publish'
import BrandBinding from '@/views/finance/brandBinding'
export * from './router'

export interface Page {
  auth?: string // 若指定，则只有用户权限中包含此 key，导航栏才显示这一项。
  path: string // 页面的完整路径
  name: string // 页面名称（显示在导航和面包屑中）
  icon?: JSX.Element // 页面图标（显示在导航中）
  // component?: (() => JSX.Element | null) | React.ComponentClass // 页面内容组件
  element?: JSX.Element | null // 页面内容组件，更新为 element
  children?: Page[] // 下级页面
}

// 页面分类
export type PageCategory = Omit<Page, 'path' | 'component'>

// ======================================================

export const pages: (PageCategory | Page)[] = [
  { path: '/', name: '前台', icon: <HomeOutlined />, element: <Dashboard /> },
  {
    auth: 'order',
    name: '订单',
    icon: <OrderIcon style={{ fontSize: '14px' }} />,
    children: [
      {
        auth: 'order:table',
        path: '/order/list',
        name: '短租订单',
        element: <OrderManage />,
        children: [
          { path: '/order/list/create', name: '新增订单', element: <CreateOrder /> },
          {
            auth: 'order:table',
            path: '/order/list/detail/:orderId',
            name: '订单详情',
            element: <OrderDetail />,
          },
          { path: '/order/list/pickUp/:orderId', name: '取车验车', element: <PickUpVehicle /> },
          { path: '/order/list/return/:orderId', name: '还车验车', element: <ReturnVehicle /> },
        ],
      },
      {
        auth: 'longOrder:manage',
        path: '/long-order/list',
        name: '长租订单',
        element: <LongOrderManage />,
        children: [
          { path: '/long-order/list/create', name: '新增订单', element: <CreateLongOrder /> },
          {
            path: '/long-order/list/detail/:orderId',
            name: '订单详情',
            element: <LongOrderDetail />,
          },
          {
            path: '/long-order/list/pickUp/:orderId',
            name: '取车验车',
            element: <LongPickUpVehicle />,
          },
          {
            path: '/long-order/list/return/:orderId',
            name: '还车验车',
            element: <LongReturnVehicle />,
          },
        ],
      },
      {
        // auth: 'order:contract',
        auth: 'order:elecContract',
        path: '/order/electronic-contract',
        name: '电子合同',
        element: <ElectronicContract />,
        children: [
          {
            auth: 'order:contract:edit',
            path: '/order/electronic-contract/agreement',
            name: '租车协议模版',
            element: <Agreement />,
          },
          {
            auth: 'order:contract:view',
            path: '/order/electronic-contract/agreement-view',
            name: '租车协议模版',
            element: <AgreementView />,
          },
          {
            auth: 'order:contract:edit',
            path: '/order/electronic-contract/validate-car',
            name: '验车单模板',
            element: <ValidateCar />,
          },
          {
            auth: 'order:contract:view',
            path: '/order/electronic-contract/validate-car-view',
            name: '验车单模板',
            element: <ValidateCarView />,
          },
          {
            auth: 'order:contract:edit',
            path: '/order/electronic-contract/rent-car',
            name: '租车单模板',
            element: <RentCar />,
          },
          {
            auth: 'order:contract:view',
            path: '/order/electronic-contract/rent-car-view',
            name: '租车单模板',
            element: <RentCatView />,
          },
        ],
      },
    ],
  },
  {
    auth: 'stock',
    name: '库存',
    icon: <StockIcon style={{ fontSize: '16px' }} />,
    children: [{ path: '/stock', name: '库存管理', element: <Stock /> }],
  },
  {
    auth: 'price',
    name: '价格',
    icon: <PriceIcon style={{ fontSize: '16px' }} />,
    children: [
      {
        auth: 'price:setting',
        path: '/price/overview',
        name: '价格概览',
        element: <PriceOverview />,
      },
      {
        auth: 'price:setting',
        path: '/price/list',
        name: '价格设置',
        element: <PriceList />,
        children: [
          { path: '/price/list/detail/:priceId', name: '租金价格设置', element: <PriceDetail /> },
          {
            path: '/price/list/creditFree',
            name: '信用免押分渠道设置',
            element: <CreditFreeByChannel />,
          },
        ],
      },
      { auth: 'price:rent', path: '/price/rent', name: '租金价格设置', element: <RentPrice /> },
      {
        auth: 'price:added',
        path: '/price/servicesList',
        name: '保险/附加服务列表',
        element: <AdditionalServicesList />,
      },
      {
        auth: 'price:addedprice',
        path: '/price/servicesPrice',
        name: '附加服务价格',
        element: <ServicePrice />,
        children: [
          {
            path: '/price/servicesPrice/batch',
            name: '批量修改附加服务价格',
            element: <BatchEditServicePrice />,
          },
        ],
      },
      {
        auth: 'price:insuranceprice',
        path: '/price/insurancePrice',
        name: '保险价格设置',
        element: <InsurancePrice />,
        children: [
          {
            path: '/price/insurancePrice/batch',
            name: '批量修改保险价格',
            element: <BatchEditInsurancePrice />,
          },
        ],
      },
      {
        auth: 'price:credit',
        path: '/price/creditFree',
        name: '信用免押分渠道设置',
        element: <CreditFreeByChannel />,
      },
      {
        auth: 'price:credit',
        path: '/price/channelPackageSetup',
        name: '渠道套餐设置',
        // @ts-ignore
        element: <ChannelPackageSetup />,
      },
      {
        path: '/price/xcLimitInquiry',
        name: '携程渠道限价查询',
        element: <XcLimitInquiry />,
      },
    ],
  },
  {
    auth: 'report',
    name: '数据中心',
    icon: <ReportIcon style={{ fontSize: '16px' }} />,
    children: [
      {
        auth: 'report:data',
        path: '/pandect/index',
        name: '数据总览',
        element: <PandectView />,
      },
      {
        auth: 'report:data',
        path: '/pandect/overview',
        name: '每日概览',
        element: <PandectOverview />,
      },
    ],
  },
  {
    auth: 'store',
    name: '门店',
    icon: <StoreIcon style={{ fontSize: '16px' }} />,
    children: [
      {
        auth: 'store:table',
        path: '/stores',
        name: '门店列表',
        element: <StoresModels />,
        children: [
          { path: '/stores/add', name: '创建门店', element: <EditStore key="add" /> },
          {
            path: '/stores/:storeId',
            name: '门店详情',
            element: <StoreDetail />,
            children: [
              {
                path: '/stores/:storeId/edit/',
                name: '编辑门店',
                element: <EditStore key="edit" />,
              },
              {
                path: '/stores/:storeId/pickup',
                name: '新增服务圈',
                element: <EditPickup key="add" />,
              },
              {
                path: '/stores/:storeId/pickup/:pickupId',
                name: '编辑服务圈',
                element: <EditPickup key="edit" />,
              },
            ],
          },
        ],
      },
      {
        auth: 'rule',
        path: '/stores/cancel-rules',
        name: '取消规则',
        element: <CancelRules />,
      },
      {
        auth: 'store:table',
        path: '/stores/allopatry',
        name: '异地取还',
        element: <Allopatry />,
      },
      {
        auth: 'admin',
        path: '/service-policies',
        name: '服务政策',
        element: <ManageServicePolicy />,
        children: [
          {
            path: '/service-policies/:policyId',
            name: '服务政策详情',
            element: <ServicePolicyDetail />,
            children: [
              {
                path: '/service-policies/:policyId/edit/',
                name: '编辑服务政策',
                element: <EditServicePolicy />,
              },
            ],
          },
        ],
      },
    ],
  },
  {
    auth: 'car',
    name: '车辆',
    icon: <CarIcon style={{ fontSize: '16px' }} />,
    children: [
      {
        auth: 'car:model',
        path: '/models',
        name: '车型管理',
        element: <VehicleModels />,
        children: [
          {
            path: '/models/add',
            name: '新增车型',
            element: <EditVehicleModel key="add" />,
          },
          {
            path: '/models/edit/:modelId',
            name: '编辑车型',
            element: <EditVehicleModel key="edit" />,
          },
          {
            path: '/models/:modelId',
            name: '车型详情',
            element: <VehicleModelDetail />,
          },
        ],
      },
      {
        auth: 'car:vehicle',
        path: '/vehicles',
        name: '车辆管理',
        element: <Vehicles />,
        children: [
          { path: '/vehicles/add', name: '新增车辆', element: <EditVehicle key="add" /> },
          {
            path: '/vehicles/edit/:vehicleId',
            name: '编辑车辆',
            element: <EditVehicle key="edit" />,
          },
          {
            path: '/vehicles/:vehicleId',
            name: '车辆详情',
            element: <VehicleDetail />,
          },
        ],
      },
      {
        auth: 'car:ticket',
        path: '/tickets',
        name: '车辆工单',
        element: <VehicleTickets />,
      },
      {
        auth: 'car:breakRules',
        path: '/illegals',
        name: '违章管理',
        element: <IllegalManage />,
        children: [
          {
            path: '/illegals/:id',
            name: '违章记录',
            element: <IllegalDetail />,
          },
        ],
      },
      {
        auth: 'car:device',
        path: '/vehicleControl',
        name: '车辆控制',
        element: <VehicleControl />,
      },
      {
        path: '/GPSService',
        name: 'GPS管理',
        element: <GPSService />,
      },
    ],
  },
  {
    auth: 'etc',
    name: 'ETC管理',
    icon: <CarIcon style={{ fontSize: '16px' }} />,
    children: [
      {
        auth: 'etc:car',
        path: '/etcCar',
        name: 'ETC车辆',
        element: <EtcCar />,
        children: [
          { path: '/etcCar/:id', name: 'ETC车辆详情', element: <EtcCarDetail /> }
        ],
      },
      {
        auth: 'etc:publish',
        path: '/etcPublish',
        name: 'ETC发行',
        element: <EtcPublish />,
      },
      {
        auth: 'etc:order',
        path: '/etcOrder',
        name: 'ETC订单',
        element: <EtcOrder />,
        children: [
          { path: '/etcOrder/:id', name: 'ETC订单详情', element: <EtcOrderDetail /> }
        ],
      },
      {
        auth: 'etc:page',
        path: '/ETCService',
        name: 'ETC服务',
        element: <ETCService />,
      },
    ],
  },
  {
    auth: 'merchant',
    name: '商家',
    icon: <MerchantIcon style={{ fontSize: '16px' }} />,
    children: [
      {
        auth: 'merchant:flair',
        path: '/merchant/certified',
        name: '资质认证',
        element: <MerchantCertified />,
      },
    ],
  },
  {
    auth: 'task',
    name: '任务',
    icon: <TaskIcon style={{ fontSize: '16px' }} />,
    children: [
      {
        auth: 'task',
        path: '/order/task',
        name: '任务管理',
        element: <OrderTaskManage />,
      },
      {
        auth: 'task:driver',
        path: '/order/driver-board',
        name: '司机看板',
        element: <DriverBoard />,
      },
    ],
  },
  {
    auth: 'bill',
    name: '账单',
    icon: <BillIcon style={{ fontSize: '16px' }} />,
    children: [
      {
        auth: 'bill:manage',
        path: '/bill/list',
        name: '账单管理',
        element: <Bill />,
      },
    ],
  },
  {
    auth: 'alipay',
    path: '/alipay',
    name: '支付宝',
    element: <Alipay />,
  },
  {
    auth: 'market',
    name: '营销',
    icon: <MarketIcon style={{ fontSize: '16px' }} />,
    children: [
      {
        auth: 'market:manage',
        path: '/publicitys/index',
        name: '营销管理',
        element: <Publicitys />,
        children: [
          {
            path: '/publicitys/index/create',
            name: '新增活动',
            element: <CreatePublicitys key="add" />,
          },
          {
            path: '/publicitys/index/create/:id',
            name: '查看活动',
            element: <CreatePublicitys key="view" />,
          },
          {
            path: '/publicitys/index/create/:type/:id',
            name: '修改活动',
            element: <CreatePublicitys key="edit" />,
          },
        ],
      },
    ],
  },
  {
    auth: 'channel',
    name: '渠道',
    icon: <ChannelIcon style={{ fontSize: '16px' }} />,
    children: [
      {
        auth: 'channel:manage',
        path: '/channel/list',
        name: '渠道管理',
        element: <ChannelManage />,
      },
    ],
  },
  {
    auth: 'user',
    name: '用户',
    icon: <UserIcon style={{ fontSize: '16px' }} />,
    children: [
      {
        auth: 'user:account',
        path: '/accounts',
        name: '用户管理',
        element: <Accounts />,
        children: [
          { path: '/accounts/add', name: '新增账号', element: <EditAccount key="add" /> },
          {
            path: '/accounts/:accountId/edit',
            name: '编辑账号',
            element: <EditAccount key="edit" />,
          },
          { path: '/accounts/:accountId', name: '账号详情', element: <AccountDetail /> },
        ],
      },
      {
        auth: 'user:role',
        path: '/roles',
        name: '权限管理',
        element: <Roles />,
      },
      {
        auth: 'user:push',
        path: '/account/notify',
        name: '消息通知',
        element: <AccountNotify />,
      },
    ],
  },
  {
    auth: 'procure',
    name: '采购商城',
    icon: <ProcureIcon style={{ fontSize: '16px' }} />,
    children: [
      {
        auth: 'procure:service',
        path: '/purchasShop',
        name: '服务商城',
        element: <PurchasShop />,
        children: [
          {
            path: '/purchasShop/purchasList',
            name: '订单列表',
            element: <PurchasList />,
          },
          // {
          //   path: '/purchasShop/purchasListGPS',
          //   name: 'GPS订单列表',
          //   component: PurchasListGPS,
          // },
        ],
      },
    ],
  },
  {
    auth: 'Finance',
    name: '财务管理',
    icon: <ReadOutlined />,
    children: [
      {
        auth: 'Finance:shortReconcilia',
        path: '/financeManagement',
        name: '短租对账',
        element: <FinanceManagement />,
      },
      {
        auth: 'Finance:incomeDetail',
        path: '/flowingRecords',
        name: '收支明细',
        element: <FlowingRecords />,
      },
      // {
      //   path: '/financialOverview',
      //   name: '财务概览',
      //   component: FinancialOverview ,
      // }
    ],
  },
  {
    name: '运维工具',
    icon: <DollarOutlined />,
    children: [
      {
        path: '/finance/recharge',
        name: '线下充值',
        element: <FinanceRecharge />,
      },
      {
        path: '/finance/paymentChannel',
        name: '支付渠道',
        element: <PaymentChannel />,
      },
      {
        path: '/finance/accountExtension',
        name: '账户临延',
        element: <AccountExtension />,
      },
      {
        path: '/finance/pullLimitData',
        name: '携程推送',
        element: <PullLimitData />,
      },
      {
        path: '/finance/pullLimitPrice',
        name: '限价拉取',
        element: <PullLimitPrice />,
      },
      {
        path: '/finance/brandBinding',
        name: '车型绑定',
        element: <BrandBinding />,
      },
      {
        path: '/finance/packageSetup',
        name: '套餐设置',
        element: <PackageSetup />,
      },
      {
        path: '/finance/codeGeneration',
        name: 'ETC二维码生成',
        element: <CodeGeneration />,
      },
    ],
  },
  {
    // auth: 'operations',
    name: '操作日志',
    path: '/log-operations',
    element: <LogOperations />,
    icon: <FileTextOutlined />,
  },
  // 保证系统信息管理在路由菜单配置末尾
  {
    name: '设置',
    path: '/system-info',
    element: <SystemInfo />,
    icon: <SettingOutlined />,
    children: [
      {
        path: '/system-info/config/:type',
        name: '新增设置',
        element: <SystemInfoConfig key="add" />,
      },
      {
        path: '/system-info/config/:type/:id',
        name: '查看设置',
        element: <SystemInfoConfig key="view" />,
      },
      {
        path: '/system-info/config/:type/:id',
        name: '修改设置',
        element: <SystemInfoConfig key="edit" />,
      },
    ],
  },
]
```
