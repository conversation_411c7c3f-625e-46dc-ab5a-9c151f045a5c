# PC 订单列表复制功能 - 组件对照表 v1.0.1

## 📋 概述

本文档详细说明 PC 订单列表复制功能原型图中每个元素与擎路组件库的对应关系，以及新增复制功能的组件实现方案。

## 🎯 核心变更组件

### 1. 复制图标组件 (新增)

**组件名称**: CopyIcon  
**对应组件库**: 基于 Ant Design 的 `CopyOutlined` 图标 + 自定义样式  
**功能描述**: 可点击的复制图标，支持复制文本到剪贴板

```typescript
interface CopyIconProps {
  text: string; // 要复制的文本
  tooltip?: string; // 提示文本
  size?: number; // 图标大小，默认16px
  className?: string; // 自定义样式类名
}
```

**实现方案**:

- 使用 `navigator.clipboard.writeText()` API 实现复制
- 提供 `document.execCommand('copy')` 备用方案
- 支持复制成功提示
- 支持 hover 效果和点击反馈

### 2. 订单号组件 (增强)

**原组件**: 普通文本显示  
**对应组件库**: `LinkButton` + `CopyIcon`  
**增强功能**: 在订单号后添加复制图标

**实现位置**: 表格 "创建时间" 列的订单号显示区域  
**变更说明**: 保持原有订单号样式，在右侧增加复制图标

### 3. 车牌号组件 (增强)

**原组件**: 车辆信息显示  
**对应组件库**: 原有车辆信息组件 + `CopyIcon`  
**增强功能**: 在车牌号后添加复制图标

**实现位置**: 表格 "预订车辆" 列的车牌号显示区域  
**变更说明**: 车牌号使用加粗字体，右侧增加复制图标

### 4. 渠道订单号组件 (新增列)

**组件名称**: 渠道订单号列  
**对应组件库**: `Table` 列配置 + `CopyIcon`  
**功能描述**: 新增表格列显示渠道订单号，支持复制功能

**实现位置**: 在 "预订车辆" 和 "自定义标签" 列之间新增  
**数据来源**: 订单的渠道信息字段

## 🏗️ 页面结构对照

### 主要布局组件

| 页面区域   | 对应组件库组件 | 说明                    |
| ---------- | -------------- | ----------------------- |
| 顶部导航栏 | `PageHeader`   | 品牌 logo、用户信息显示 |
| 左侧导航   | `Menu`         | 侧边栏导航菜单          |
| 标签页     | `Tabs`         | 页面标签页导航          |
| 搜索表单   | `MySearchForm` | 订单筛选和查询表单      |
| 统计标签   | `Tabs`         | 订单状态统计和切换      |
| 操作栏     | 自定义布局     | 新增、导出等操作按钮    |
| 订单表格   | `MyProTable`   | 数据表格显示和操作      |

### 表单组件对照

| 表单元素       | 对应组件库组件     | 配置说明       |
| -------------- | ------------------ | -------------- |
| 订单信息输入框 | `AlphaInput`       | 支持手机号输入 |
| 订单来源选择   | `EnumSelect`       | 下拉选择组件   |
| 车型输入框     | `ChooseModels`     | 车型选择器组件 |
| 订单状态标签   | `CardCheckbox`     | 卡片式多选组件 |
| 查询按钮       | `Button` (primary) | 主要操作按钮   |
| 重置按钮       | `Button` (default) | 次要操作按钮   |

### 表格列配置

| 列名       | 宽度  | 对应组件          | 特殊功能     |
| ---------- | ----- | ----------------- | ------------ |
| 创建时间   | 180px | 文本 + `CopyIcon` | 订单号复制   |
| 状态       | 100px | `Badge`           | 状态标签显示 |
| 预订车辆   | 180px | 文本 + `CopyIcon` | 车牌号复制   |
| 渠道订单号 | 180px | 文本 + `CopyIcon` | 新增复制功能 |
| 自定义标签 | 150px | `Tag`             | 标签显示     |
| 取还       | 240px | 文本              | 时间信息     |
| 承租人     | 120px | 文本              | 用户信息     |
| 订单总价   | 100px | `MoneyInput`      | 金额格式化   |

## 💻 技术实现对照

### CSS 类名对照

| 原型图样式类     | 对应组件库类名                | 功能说明       |
| ---------------- | ----------------------------- | -------------- |
| `.copy-icon`     | 自定义样式                    | 复制图标样式   |
| `.copy-toast`    | `notification`                | 复制成功提示   |
| `.order-number`  | `.ant-typography-link`        | 订单号链接样式 |
| `.vehicle-plate` | `.ant-typography-text-strong` | 车牌号加粗显示 |
| `.channel-order` | `.ant-typography-link`        | 渠道订单号样式 |

### JavaScript 功能对照

| 功能            | 实现方式                          | 对应 API           |
| --------------- | --------------------------------- | ------------------ |
| 复制到剪贴板    | `navigator.clipboard.writeText()` | Clipboard API      |
| 备用复制方案    | `document.execCommand('copy')`    | Legacy API         |
| 复制成功提示    | `message.success()`               | Ant Design Message |
| 图标 hover 效果 | CSS `:hover`                      | 样式交互           |

## 🔧 开发实现建议

### 1. 组件复用优先级

1. **最高优先级**: 使用现有组件库组件
2. **中等优先级**: 基于现有组件进行功能扩展
3. **最低优先级**: 创建全新组件（仅在必要时）

### 2. 实现顺序建议

1. 首先实现通用的 `CopyIcon` 组件
2. 在现有订单号、车牌号字段中集成复制功能
3. 新增渠道订单号表格列
4. 实现复制成功的用户反馈
5. 进行功能测试和样式调优

### 3. 兼容性考虑

- 复制功能支持现代浏览器的 Clipboard API
- 提供传统浏览器的 execCommand 备用方案
- 确保在 HTTPS 环境下复制功能正常工作
- 处理复制失败的异常情况

## 📝 注意事项

1. **数据安全**: 复制功能仅限于前端显示的数据，不涉及敏感信息泄露
2. **用户体验**: 复制成功后应提供明确的视觉反馈
3. **性能优化**: 复制图标使用 SVG 格式，确保在高分辨率设备上显示清晰
4. **可访问性**: 为复制按钮添加适当的 `aria-label` 和 `title` 属性
