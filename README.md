# 擎路 AI - PRD 管理仓库

## 项目概述

本项目是一个用于管理 H5 和 PC 项目 PRD（产品需求文档）生成的仓库，提供完整的项目文档管理、版本控制和开发提示词生成功能。

## 主要功能

### 🎯 核心功能

- **组件库管理**：管理 H5 和 PC 组件库文档和代码
- **项目架构文档**：维护项目技术架构和设计文档
- **路由文件管理**：统一管理前端路由配置
- **PRD 生成**：基于需求自动生成标准化 PRD 文档
- **开发提示词**：生成针对性的开发指导提示词
- **版本管理**：完整的版本控制和变更追踪

### 🔧 辅助功能

- **提示词模版**：标准化的前端开发提示词模版
- **质量检查**：内置的文档质量检查清单
- **MCP 集成**：支持通过 MCP 访问外部 SaaS 服务
- **自动化工作流**：规范化的 PRD 生成和管理流程

## 项目结构

```
qinglu-ai/
├── modules/                      # 核心功能模块
│   ├── h5/                       # H5相关文件
│   │   ├── comp-lib/             # H5组件库（git子仓库）
│   │   ├── project-doc/          # H5项目架构文档
│   │   └── router-file/          # H5路由文件
│   └── pc/                       # PC相关文件
│       ├── comp-lib/             # PC组件库（git子仓库）
│       ├── project-doc/          # PC项目架构文档
│       └── router-file/          # PC路由文件
├── prd/                          # PRD文档存储
│   └── v1.0.0/                   # 版本化目录
│       ├── h5/                   # H5项目PRD
│       └── pc/                   # PC项目PRD
├── prompt/                       # 开发提示词存储
│   └── v1.0.0/                   # 版本化目录
│       ├── h5/                   # H5开发提示词
│       └── pc/                   # PC开发提示词
├── prompt-template/              # 提示词模版
│   └── 前端开发提示词模版.md
├── version-manage/               # 版本管理
│   ├── version.json              # 版本配置
│   ├── CHANGELOG.md              # 变更日志
│   └── README.md                 # 版本管理说明
├── 全局PRD生成提示词.md          # 主要的PRD生成指导
├── 需求.md                       # 项目需求文档
└── README.md                     # 本文档
```

## 快速开始

### 1. 克隆仓库

```bash
git clone <repository-url>
cd qinglu-ai
```

### 2. 初始化组件库子仓库

```bash
# 如果需要添加组件库子仓库
git submodule add <h5-comp-lib-url> modules/h5/comp-lib
git submodule add <pc-comp-lib-url> modules/pc/comp-lib
```

### 3. 查看当前版本

```bash
cat version-manage/version.json | grep "currentVersion"
```

## 使用指南

### PRD 生成流程

1. **准备阶段**

   - 确定项目类型（H5/PC）
   - 准备需求描述文档
   - 检查组件库和架构文档是否最新

2. **资源收集**

   - 查阅对应的组件库文档（`modules/h5/comp-lib/` 或 `modules/pc/comp-lib/`）
   - 参考项目架构文档（`modules/h5/project-doc/` 或 `modules/pc/project-doc/`）
   - 确认路由配置（`modules/h5/router-file/` 或 `modules/pc/router-file/`）

3. **PRD 生成**

   - 使用 `全局PRD生成提示词.md` 中的指导流程
   - 结合 MCP 服务获取外部数据
   - 按照标准模版生成 PRD 文档

4. **文档保存**
   - HTML 格式 PRD 保存到 `prd/{版本号}/{项目类型}/`
   - 开发提示词保存到 `prompt/{版本号}/{项目类型}/`
   - 更新版本管理信息

### 版本管理

#### 创建新版本

```bash
# 1. 创建版本目录
mkdir -p prd/v1.1.0/h5 prd/v1.1.0/pc
mkdir -p prompt/v1.1.0/h5 prompt/v1.1.0/pc

# 2. 更新版本配置文件
# 编辑 version-manage/version.json

# 3. 更新变更日志
# 编辑 version-manage/CHANGELOG.md
```

#### 查看版本历史

```bash
# 查看所有版本
cat version-manage/version.json | jq '.versions'

# 查看变更日志
cat version-manage/CHANGELOG.md
```

### 提示词模版使用

1. **选择对应模版**

   - H5 项目：使用 H5 项目开发提示词模版
   - PC 项目：使用 PC 项目开发提示词模版

2. **自定义配置**

   - 替换模版中的占位符
   - 根据项目需求调整开发要求
   - 添加特定的技术栈配置

3. **生成完整提示词**
   - 结合 PRD 内容生成完整的开发指导
   - 保存到对应版本目录

## 最佳实践

### 文件管理

- 严格按照目录结构存放文件
- 使用语义化的版本号
- 定期备份重要配置文件
- 保持文档和代码的同步更新

### 版本控制

- 每次重大变更都要创建新版本
- 详细记录变更内容和原因
- 使用 git 标签标记重要版本
- 定期清理过时的版本文件

### 质量保证

- 使用质量检查清单验证 PRD
- 定期 Review 生成的文档质量
- 收集用户反馈并持续改进
- 建立标准化的评审流程

## 工具和服务

### MCP 服务集成

本项目支持通过 MCP（Model Context Protocol）访问外部 SaaS 服务：

- H5/PC 的 SaaS 服务数据获取
- API 接口文档自动同步
- 实时数据查询和分析

### 推荐工具

- **文档编辑**：Markdown 编辑器、HTML 编辑器
- **版本控制**：Git、GitHub/GitLab
- **文档预览**：Markdown 预览工具、浏览器
- **API 工具**：Postman、curl、MCP 客户端

## 贡献指南

### 提交规范

遵循[约定式提交](https://www.conventionalcommits.org/zh-hans/v1.0.0/)规范：

```
<类型>[可选的作用域]: <描述>

[可选的正文]

[可选的脚注]
```

类型包括：

- `feat`：新功能
- `fix`：Bug 修复
- `docs`：文档更新
- `style`：代码格式修改
- `refactor`：代码重构
- `perf`：性能优化
- `test`：测试相关
- `chore`：构建相关

### 开发流程

1. Fork 项目并创建 feature 分支
2. 进行开发和测试
3. 提交代码并创建 Pull Request
4. 代码 Review 和合并

## 常见问题

### Q: 如何添加新的组件库？

A: 在对应的 `modules/h5/comp-lib/` 或 `modules/pc/comp-lib/` 目录下添加组件文档，并更新相关的架构文档。

### Q: PRD 生成失败怎么办？

A: 检查以下几点：

1. 确认所有必需的资源文件是否存在
2. 验证 MCP 服务连接是否正常
3. 检查需求描述是否完整清晰
4. 参考质量检查清单排查问题

### Q: 如何自定义提示词模版？

A: 编辑 `prompt-template/前端开发提示词模版.md` 文件，根据团队习惯和项目特点进行调整。

### Q: 版本管理出现冲突如何解决？

A: 参考 `version-manage/README.md` 中的故障排除部分，或从 git 历史中恢复到稳定版本。

## 更新日志

详细的版本变更记录请查看 [CHANGELOG.md](version-manage/CHANGELOG.md)。

## 许可证

本项目采用 MIT 许可证 - 详见 LICENSE 文件。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 创建 Issue
- 提交 Pull Request
- 邮件联系：[<EMAIL>]

---

**快速链接**

- [全局 PRD 生成提示词](全局PRD生成提示词.md)
- [版本管理说明](version-manage/README.md)
- [前端开发提示词模版](prompt-template/前端开发提示词模版.md)
- [变更日志](version-manage/CHANGELOG.md)
