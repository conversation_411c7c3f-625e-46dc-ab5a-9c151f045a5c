<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>违章管理 - 擎路PC</title>
    <!-- 引入线上系统的CSS和JS资源 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd@5.12.8/dist/reset.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd@5.12.8/dist/antd.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/antd@5.12.8/dist/antd.min.js"></script>
    <style>
      /* 页面特定样式，确保与线上系统完全一致 */
      body {
        font-family: -apple-system, "system-ui", "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        margin: 0;
        padding: 0;
        background-color: #f5f5f5;
        color: rgba(0, 0, 0, 0.85);
        font-size: 12px;
        line-height: 18.858px;
      }

      .layout {
        display: flex;
        min-height: 100vh;
      }

      .sidebar {
        width: 200px;
        background-color: #001529;
        color: white;
        padding: 16px 0;
      }

      .main-content {
        flex: 1;
        background-color: #f0f2f5;
        padding: 24px;
      }

      .page-header {
        background-color: white;
        padding: 16px 24px;
        margin-bottom: 16px;
        border-radius: 6px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
      }

      .search-form {
        background-color: white;
        padding: 24px;
        margin-bottom: 16px;
        border-radius: 6px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
      }

      .form-row {
        display: flex;
        gap: 16px;
        margin-bottom: 16px;
        flex-wrap: wrap;
      }

      .form-item {
        display: flex;
        align-items: center;
        min-width: 200px;
      }

      .form-label {
        width: 100px;
        text-align: right;
        margin-right: 8px;
        color: rgba(0, 0, 0, 0.85);
        font-size: 12px;
      }

      .form-control {
        flex: 1;
        min-width: 120px;
      }

      .ant-input, .ant-select {
        width: 100%;
        height: 32px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        padding: 4px 11px;
        font-size: 12px;
        transition: all 0.3s;
      }

      .ant-input:focus, .ant-select:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }

      .ant-btn {
        height: 32px;
        padding: 4px 15px;
        border-radius: 6px;
        font-size: 12px;
        border: 1px solid #d9d9d9;
        background-color: white;
        color: rgba(0, 0, 0, 0.85);
        cursor: pointer;
        transition: all 0.3s;
      }

      .ant-btn-primary {
        background-color: #1890ff;
        border-color: #1890ff;
        color: white;
      }

      .ant-btn-primary:hover {
        background-color: #40a9ff;
        border-color: #40a9ff;
      }

      .ant-btn:hover {
        border-color: #40a9ff;
        color: #40a9ff;
      }

      .table-container {
        background-color: white;
        border-radius: 6px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        overflow: hidden;
      }

      .table-header {
        padding: 16px 24px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .table-title {
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }

      .table-actions {
        display: flex;
        gap: 8px;
      }

      .ant-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        font-size: 12px;
        font-family: -apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        color: rgba(0, 0, 0, 0.85);
        background-color: rgba(0, 0, 0, 0);
      }

      .ant-table thead th {
        background-color: rgba(0, 0, 0, 0);
        color: rgba(0, 0, 0, 0.85);
        font-weight: 400;
        font-size: 12px;
        height: 70.7031px;
        border-bottom: 0px none rgb(128, 128, 128);
        padding: 16px;
        text-align: left;
        vertical-align: middle;
        border: none;
        position: relative;
      }

      .ant-table tbody td {
        padding: 16px;
        border: none;
        text-align: left;
        vertical-align: middle;
        font-size: 12px;
        line-height: 18.858px;
        border-bottom: 1px solid #f0f0f0;
      }

      .ant-table tbody tr:hover {
        background-color: #fafafa;
      }

      .status-tag {
        display: inline-block;
        padding: 5px 12px;
        border-radius: 2px;
        font-size: 12px;
        line-height: 1;
        border: 1px solid #d9d9d9;
        background-color: #fafafa;
        color: rgba(0, 0, 0, 0.65);
      }

      .status-success {
        background-color: #f6ffed;
        border-color: #b7eb8f;
        color: #52c41a;
      }

      .status-error {
        background-color: #fff2f0;
        border-color: #ffccc7;
        color: #ff4d4f;
      }

      .status-warning {
        background-color: #fffbe6;
        border-color: #ffe58f;
        color: #faad14;
      }

      .status-default {
        background-color: #fafafa;
        border-color: #d9d9d9;
        color: rgba(0, 0, 0, 0.65);
      }

      /* 转移失败提示图标样式 */
      .transfer-fail-icon {
        display: inline-flex;
        align-items: center;
        margin-left: 4px;
        cursor: pointer;
        color: #ff4d4f;
        font-size: 14px;
      }

      .transfer-fail-icon:hover {
        color: #ff7875;
      }

      /* Popover 样式 */
      .ant-popover {
        font-size: 12px;
      }

      .ant-popover-inner {
        background-color: white;
        border-radius: 6px;
        box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
        border: 1px solid #f0f0f0;
      }

      .ant-popover-arrow {
        display: none;
      }

      .popover-content {
        padding: 8px 12px;
        color: rgba(0, 0, 0, 0.85);
        font-size: 12px;
        line-height: 1.5;
        max-width: 300px;
      }

      /* 改动点标注样式 */
      .change-highlight {
        position: relative;
        border: 2px dashed #ff4d4f !important;
        background-color: rgba(255, 77, 79, 0.1) !important;
      }
      .change-highlight::before {
        content: "新增功能";
        position: absolute;
        top: -8px;
        left: 8px;
        background: #ff4d4f;
        color: white;
        padding: 2px 6px;
        font-size: 10px;
        border-radius: 2px;
        z-index: 1000;
      }
      .modify-highlight {
        position: relative;
        border: 2px dashed #fa8c16 !important;
        background-color: rgba(250, 140, 22, 0.1) !important;
      }
      .modify-highlight::before {
        content: "修改功能";
        position: absolute;
        top: -8px;
        left: 8px;
        background: #fa8c16;
        color: white;
        padding: 2px 6px;
        font-size: 10px;
        border-radius: 2px;
        z-index: 1000;
      }

      /* 分页样式 */
      .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 16px 24px;
        border-top: 1px solid #f0f0f0;
      }

      .pagination-item {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 32px;
        height: 32px;
        margin: 0 4px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        background-color: white;
        color: rgba(0, 0, 0, 0.65);
        cursor: pointer;
        transition: all 0.3s;
        font-size: 12px;
      }

      .pagination-item:hover {
        border-color: #1890ff;
        color: #1890ff;
      }

      .pagination-item.active {
        background-color: #1890ff;
        border-color: #1890ff;
        color: white;
      }

      .pagination-item.disabled {
        color: rgba(0, 0, 0, 0.25);
        cursor: not-allowed;
        border-color: #f0f0f0;
      }

      /* 统计信息样式 */
      .stats-info {
        background-color: white;
        padding: 16px 24px;
        margin-bottom: 16px;
        border-radius: 6px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .stats-left {
        display: flex;
        align-items: center;
        gap: 24px;
      }

      .stats-item {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .stats-label {
        color: rgba(0, 0, 0, 0.65);
        font-size: 12px;
      }

      .stats-value {
        color: #1890ff;
        font-weight: 500;
        font-size: 14px;
      }

      .stats-right {
        display: flex;
        gap: 8px;
      }

      .link-text {
        color: #1890ff;
        cursor: pointer;
        font-size: 12px;
      }

      .link-text:hover {
        color: #40a9ff;
      }
    </style>
  </head>
  <body>
    <div class="layout">
      <!-- 侧边栏 -->
      <div class="sidebar">
        <div style="padding: 0 16px; margin-bottom: 24px;">
          <h3 style="color: white; margin: 0; font-size: 16px;">擎路管理系统</h3>
        </div>
        <div style="padding: 0 16px;">
          <div style="color: rgba(255, 255, 255, 0.65); font-size: 12px; margin-bottom: 8px;">车辆</div>
          <div style="color: white; font-size: 12px; padding: 8px 0; background-color: #1890ff; border-radius: 4px; padding-left: 8px;">违章管理</div>
        </div>
      </div>

      <!-- 主内容区 -->
      <div class="main-content">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 style="margin: 0; font-size: 20px; font-weight: 500;">违章管理</h1>
        </div>

        <!-- 搜索表单 -->
        <div class="search-form">
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">门店:</label>
              <div class="form-control">
                <select class="ant-select">
                  <option>请选择门店</option>
                  <option>353-博乐-神风租车22</option>
                </select>
              </div>
            </div>
            <div class="form-item">
              <label class="form-label">关联订单号:</label>
              <div class="form-control">
                <input type="text" class="ant-input" placeholder="请输入订单号" />
              </div>
            </div>
            <div class="form-item">
              <label class="form-label">车牌号:</label>
              <div class="form-control">
                <input type="text" class="ant-input" placeholder="请输入车牌号" />
              </div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">创建日期:</label>
              <div class="form-control">
                <input type="date" class="ant-input" />
              </div>
            </div>
            <div class="form-item">
              <label class="form-label">至:</label>
              <div class="form-control">
                <input type="date" class="ant-input" />
              </div>
            </div>
            <div class="form-item">
              <label class="form-label">操作时间:</label>
              <div class="form-control">
                <input type="date" class="ant-input" />
              </div>
            </div>
            <div class="form-item">
              <label class="form-label">至:</label>
              <div class="form-control">
                <input type="date" class="ant-input" />
              </div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">违章时间:</label>
              <div class="form-control">
                <input type="date" class="ant-input" />
              </div>
            </div>
            <div class="form-item">
              <label class="form-label">至:</label>
              <div class="form-control">
                <input type="date" class="ant-input" />
              </div>
            </div>
            <div class="form-item">
              <label class="form-label">交管违章处理状态:</label>
              <div class="form-control">
                <select class="ant-select">
                  <option>请选择</option>
                  <option>已处理</option>
                  <option>未处理</option>
                </select>
              </div>
            </div>
            <div class="form-item">
              <label class="form-label">转移状态:</label>
              <div class="form-control">
                <select class="ant-select">
                  <option>请选择</option>
                  <option>上报失败</option>
                  <option>已终止</option>
                  <option>转移成功</option>
                </select>
              </div>
            </div>
          </div>
          <div class="form-row">
            <div style="display: flex; gap: 8px; margin-left: 100px;">
              <button class="ant-btn">待处理</button>
              <button class="ant-btn">已处理</button>
              <button class="ant-btn">无需处理</button>
            </div>
          </div>
          <div class="form-row">
            <div style="display: flex; gap: 8px; margin-left: 100px;">
              <button class="ant-btn">违章查询</button>
              <button class="ant-btn ant-btn-primary">查 询</button>
              <button class="ant-btn">重 置</button>
            </div>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-info">
          <div class="stats-left">
            <div class="stats-item">
              <span class="stats-label">待处理违章：</span>
              <span class="stats-value">193</span>
              <span class="stats-label">条</span>
            </div>
            <div class="stats-item">
              <span class="stats-label">当前使用套餐：违章转移套餐</span>
              <span class="link-text">查看明细</span>
            </div>
          </div>
          <div class="stats-right">
            <span class="link-text">切换套餐</span>
            <span class="link-text">自动查询规则设置</span>
          </div>
        </div>

        <!-- 表格容器 -->
        <div class="table-container">
          <div class="table-header">
            <div class="table-title">215条违章记录</div>
            <div style="display: flex; align-items: center; gap: 16px;">
              <span style="font-size: 12px; color: rgba(0, 0, 0, 0.65);">排序方式：最后修改时间 近→远</span>
              <div class="table-actions">
                <button class="ant-btn">导 入</button>
                <button class="ant-btn">导 出</button>
                <button class="ant-btn ant-btn-primary">新 增</button>
              </div>
            </div>
          </div>

          <!-- 表格 -->
          <table class="ant-table">
            <thead>
              <tr>
                <th>门店</th>
                <th>车牌号/车型信息</th>
                <th>违章城市</th>
                <th>违章时间</th>
                <th>罚分/罚款</th>
                <th>合计金额</th>
                <th>交管违章处理状态</th>
                <th>单据处理状态</th>
                <th class="change-highlight">转移状态</th>
                <th>订单号</th>
                <th>创建时间</th>
                <th>最新操作时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>353-博乐-神风租车22</td>
                <td>
                  <div>琼AP3N99</div>
                  <div style="color: rgba(0, 0, 0, 0.45); font-size: 11px;">MPV 1401-别克 GL8 2022款 ES陆尊 653T 智慧旗舰型</div>
                </td>
                <td>宁波</td>
                <td>2024-05-17 19:06:00</td>
                <td>1分/2元</td>
                <td>2元</td>
                <td>-</td>
                <td><span class="status-tag status-success">•已处理</span></td>
                <td class="change-highlight">
                  <span class="status-tag status-error">上报失败</span>
                  <span class="transfer-fail-icon" title="转移失败原因：网络连接超时，请稍后重试">⚠️</span>
                </td>
                <td><span class="link-text">466858</span></td>
                <td>2024-05-17 18:20:00</td>
                <td>2024-06-06 17:22:02</td>
                <td>
                  <span class="link-text">详情</span>
                  <span class="link-text">删除</span>
                </td>
              </tr>
              <tr>
                <td>353-博乐-神风租车22</td>
                <td>
                  <div>云K7210Y</div>
                  <div style="color: rgba(0, 0, 0, 0.45); font-size: 11px;">三厢车 本田1378- 凌派 2019款 180Turbo CVT舒适版 国V 有天窗</div>
                </td>
                <td>北京</td>
                <td>2024-05-29 18:10:00</td>
                <td>2分/2元</td>
                <td>2元</td>
                <td>-</td>
                <td><span class="status-tag status-success">•已处理</span></td>
                <td class="change-highlight">
                  <span class="status-tag status-warning">已终止</span>
                  <span class="transfer-fail-icon" title="转移失败原因：用户主动取消转移操作">⚠️</span>
                </td>
                <td><span class="link-text">467553</span></td>
                <td>2024-05-29 15:11:04</td>
                <td>2024-05-29 15:11:04</td>
                <td>
                  <span class="link-text">详情</span>
                  <span class="link-text">删除</span>
                </td>
              </tr>
              <tr>
                <td>353-博乐-神风租车22</td>
                <td>
                  <div>云K7210Y</div>
                  <div style="color: rgba(0, 0, 0, 0.45); font-size: 11px;">三厢车 本田1378- 凌派 2019款 180Turbo CVT舒适版 国V 有天窗</div>
                </td>
                <td>北京</td>
                <td>2024-05-29 17:22:00</td>
                <td>1分/1元</td>
                <td>1元</td>
                <td>-</td>
                <td><span class="status-tag status-success">•已处理</span></td>
                <td class="change-highlight">
                  <span class="status-tag status-warning">已终止</span>
                  <span class="transfer-fail-icon" title="转移失败原因：系统维护中，请稍后重试">⚠️</span>
                </td>
                <td><span class="link-text">467550</span></td>
                <td>2024-05-29 14:23:03</td>
                <td>2024-05-29 14:23:03</td>
                <td>
                  <span class="link-text">详情</span>
                  <span class="link-text">删除</span>
                </td>
              </tr>
              <tr>
                <td>353-博乐-神风租车22</td>
                <td>
                  <div>粤AA19269</div>
                  <div style="color: rgba(0, 0, 0, 0.45); font-size: 11px;">掀背车 1409-Polestar极星 Polestar 2 2021款 双电机长续航</div>
                </td>
                <td>佛山</td>
                <td>2023-09-16 16:00:00</td>
                <td>罚分获取失败/200元</td>
                <td>200元</td>
                <td><span class="status-tag status-success">已处理</span></td>
                <td><span class="status-tag status-default">•待处理</span></td>
                <td class="change-highlight">
                  <span class="status-tag status-default">-</span>
                </td>
                <td><span class="link-text">暂无订单</span></td>
                <td>2024-05-29 14:10:06</td>
                <td>2024-05-29 14:10:06</td>
                <td>
                  <span class="link-text">详情</span>
                  <span class="link-text">处理</span>
                  <span class="link-text">删除</span>
                </td>
              </tr>
              <tr>
                <td>353-博乐-神风租车22</td>
                <td>
                  <div>粤AA19269</div>
                  <div style="color: rgba(0, 0, 0, 0.45); font-size: 11px;">掀背车 1409-Polestar极星 Polestar 2 2021款 双电机长续航</div>
                </td>
                <td>广州</td>
                <td>2023-08-25 14:38:00</td>
                <td>1分/200元</td>
                <td>200元</td>
                <td><span class="status-tag status-success">已处理</span></td>
                <td><span class="status-tag status-default">•待处理</span></td>
                <td class="change-highlight">
                  <span class="status-tag status-default">-</span>
                </td>
                <td><span class="link-text">暂无订单</span></td>
                <td>2024-05-29 14:10:05</td>
                <td>2024-05-29 14:10:05</td>
                <td>
                  <span class="link-text">详情</span>
                  <span class="link-text">处理</span>
                  <span class="link-text">删除</span>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- 分页 -->
          <div class="pagination">
            <div class="pagination-item disabled">‹</div>
            <div class="pagination-item active">1</div>
            <div class="pagination-item">2</div>
            <div class="pagination-item">3</div>
            <div class="pagination-item">4</div>
            <div class="pagination-item">5</div>
            <div class="pagination-item">⋯</div>
            <div class="pagination-item">22</div>
            <div class="pagination-item">›</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 交互脚本，确保功能完整 -->
    <script>
      // Mock数据定义
      const mockData = {
        transferFailReasons: {
          '466858': '网络连接超时，请稍后重试',
          '467553': '用户主动取消转移操作',
          '467550': '系统维护中，请稍后重试'
        }
      };

      // 转移失败提示功能
      function initTransferFailTooltips() {
        const failIcons = document.querySelectorAll('.transfer-fail-icon');
        failIcons.forEach(icon => {
          icon.addEventListener('mouseenter', function(e) {
            const reason = this.getAttribute('title');
            if (reason) {
              // 这里可以显示自定义的 Popover
              console.log('显示转移失败原因:', reason);
            }
          });
        });
      }

      // 改动点标注逻辑
      function highlightChanges() {
        // 标注新增和修改的功能区域
        const transferStatusColumn = document.querySelector('th.change-highlight');
        const transferStatusCells = document.querySelectorAll('td.change-highlight');
        
        console.log('已标注转移状态列的新增功能');
      }

      // 页面加载完成后初始化
      document.addEventListener('DOMContentLoaded', function() {
        initTransferFailTooltips();
        highlightChanges();
      });

      // 模拟 Popover 显示
      function showPopover(content, target) {
        // 这里可以实现自定义的 Popover 组件
        console.log('显示 Popover:', content);
      }
    </script>
  </body>
</html> 