# PC 车辆资源检测功能 - 组件对照表 v1.0.2

## 文档信息

- **项目**: 擎路 SaaS 管理系统
- **功能**: 车辆资源检测
- **版本**: v1.0.2
- **平台**: PC 端
- **创建时间**: 2025-01-15

## 组件映射说明

本文档详细说明原型稿中每个 UI 元素与`modules/pc/comp-lib/qinglu-ant`组件库的对应关系，确保开发实现时的组件复用和规范性。

## 页面布局组件

### 1. 整体布局

| 原型稿元素   | 组件库对应       | 说明                | 位置标识        |
| ------------ | ---------------- | ------------------- | --------------- |
| 管理系统布局 | `Layout`         | Ant Design 布局组件 | 最外层容器      |
| 左侧导航栏   | `Layout.Sider`   | 侧边栏布局组件      | `.sidebar`      |
| 主内容区域   | `Layout.Content` | 内容区域组件        | `.main-content` |
| 顶部头部     | `Layout.Header`  | 页头组件            | `.header`       |

### 2. 导航组件

| 原型稿元素 | 组件库对应   | 说明                    | 位置标识      |
| ---------- | ------------ | ----------------------- | ------------- |
| 左侧菜单   | `Menu`       | 导航菜单组件，dark 主题 | `.nav-menu`   |
| 菜单项     | `Menu.Item`  | 单个菜单项              | `.menu-item`  |
| 面包屑导航 | `Breadcrumb` | 面包屑导航组件          | `.breadcrumb` |

## 表单和输入组件

### 3. 筛选表单区域

| 原型稿元素 | 组件库对应     | 说明               | 位置标识                   |
| ---------- | -------------- | ------------------ | -------------------------- |
| 筛选表单   | `Form`         | 表单容器组件       | `.filter-form`             |
| 表单项     | `Form.Item`    | 表单项包装器       | `.form-item`               |
| 下拉选择器 | `Select`       | 选择器组件         | `.form-select`             |
| 日期选择器 | `DatePicker`   | 日期选择组件       | `.form-input[type="date"]` |
| 复选框     | `Checkbox`     | 复选框组件         | 仅显示异常车辆选项         |
| 搜索输入框 | `Input.Search` | 带搜索图标的输入框 | `.search-input`            |

### 4. 按钮组件

| 原型稿元素 | 组件库对应              | 说明         | 位置标识       |
| ---------- | ----------------------- | ------------ | -------------- |
| 主要按钮   | `Button` type="primary" | 蓝色主按钮   | `.btn-primary` |
| 默认按钮   | `Button`                | 默认白色按钮 | `.btn`         |
| 危险按钮   | `Button` type="danger"  | 红色危险按钮 | `.btn-danger`  |

## 数据展示组件

### 5. 统计卡片

| 原型稿元素 | 组件库对应  | 说明              | 位置标识      |
| ---------- | ----------- | ----------------- | ------------- |
| 统计卡片   | `Card`      | 统计数据展示卡片  | `.stat-card`  |
| 统计数值   | `Statistic` | 统计数值组件      | `.stat-value` |
| 趋势图标   | `Icon`      | 上升/下降箭头图标 | `.stat-trend` |

### 6. 数据表格

| 原型稿元素 | 组件库对应     | 说明         | 位置标识      |
| ---------- | -------------- | ------------ | ------------- |
| 数据表格   | `Table`        | 数据表格组件 | `.data-table` |
| 表格列     | `Table.Column` | 表格列定义   | `<th>` 元素   |
| 分页器     | `Pagination`   | 分页组件     | `.pagination` |

### 7. 状态和标签

| 原型稿元素 | 组件库对应            | 说明         | 位置标识          |
| ---------- | --------------------- | ------------ | ----------------- |
| 状态标签   | `Tag`                 | 状态标签组件 | `.status-tag`     |
| 成功状态   | `Tag` color="success" | 绿色成功标签 | `.status-normal`  |
| 错误状态   | `Tag` color="error"   | 红色错误标签 | `.status-error`   |
| 警告状态   | `Tag` color="warning" | 橙色警告标签 | `.status-warning` |

### 8. 图表组件

| 原型稿元素 | 组件库对应          | 说明             | 位置标识           |
| ---------- | ------------------- | ---------------- | ------------------ |
| 图表容器   | `Card`              | 图表外层卡片容器 | `.chart-card`      |
| 饼图       | Chart.js + `canvas` | 异常类型分布饼图 | `#errorTypeChart`  |
| 折线图     | Chart.js + `canvas` | 异常趋势折线图   | `#errorTrendChart` |

## 交互组件

### 9. 标签页

| 原型稿元素 | 组件库对应     | 说明       | 位置标识    |
| ---------- | -------------- | ---------- | ----------- |
| 标签页容器 | `Tabs`         | 标签页组件 | `.tab-nav`  |
| 标签页项   | `Tabs.TabPane` | 单个标签页 | `.tab-item` |

### 10. 模态框

| 原型稿元素 | 组件库对应     | 说明           | 位置标识              |
| ---------- | -------------- | -------------- | --------------------- |
| 模态框     | `Modal`        | 弹窗组件       | `#vehicleDetailModal` |
| 模态框头部 | `Modal.Header` | 弹窗头部       | `.modal-header`       |
| 模态框内容 | `Modal.Body`   | 弹窗内容区     | `.modal-body`         |
| 模态框底部 | `Modal.Footer` | 弹窗底部操作区 | `.modal-footer`       |

## 工具类组件

### 11. 栅格系统

| 原型稿元素 | 组件库对应 | 说明   | 位置标识      |
| ---------- | ---------- | ------ | ------------- |
| 栅格容器   | `Row`      | 行容器 | CSS Grid 替代 |
| 栅格列     | `Col`      | 列容器 | CSS Grid 替代 |

### 12. 空间和布局

| 原型稿元素 | 组件库对应 | 说明         | 位置标识     |
| ---------- | ---------- | ------------ | ------------ |
| 间距       | `Space`    | 元素间距组件 | CSS gap 替代 |
| 分割线     | `Divider`  | 分割线组件   | 边框样式实现 |

## 主题配置

### 13. 色彩系统

| 色彩名称 | Ant Design Token     | CSS 变量    | 用途                   |
| -------- | -------------------- | ----------- | ---------------------- |
| 主要色   | `colorPrimary`       | `#1890ff`   | 主按钮、链接、选中状态 |
| 成功色   | `colorSuccess`       | `#52c41a`   | 成功状态、正常标签     |
| 警告色   | `colorWarning`       | `#faad14`   | 警告状态、部分成功     |
| 错误色   | `colorError`         | `#ff4d4f`   | 错误状态、异常标签     |
| 文字主色 | `colorText`          | `#000000d9` | 主要文字内容           |
| 文字次色 | `colorTextSecondary` | `#00000073` | 次要文字内容           |
| 背景色   | `colorBgLayout`      | `#f0f2f5`   | 页面背景色             |

### 14. 尺寸规范

| 尺寸名称 | Token           | 数值   | 用途             |
| -------- | --------------- | ------ | ---------------- |
| 小间距   | `marginXS`      | `8px`  | 元素间小间距     |
| 默认间距 | `margin`        | `16px` | 标准间距         |
| 大间距   | `marginLG`      | `24px` | 卡片、区块间距   |
| 按钮高度 | `controlHeight` | `36px` | 表单控件标准高度 |
| 圆角     | `borderRadius`  | `6px`  | 标准圆角大小     |

## 响应式断点

| 断点名称 | 屏幕宽度 | 布局变化             |
| -------- | -------- | -------------------- |
| xs       | < 576px  | 单列布局，隐藏侧边栏 |
| sm       | ≥ 576px  | 保持基本布局         |
| md       | ≥ 768px  | 标准双列布局         |
| lg       | ≥ 992px  | 完整多列布局         |
| xl       | ≥ 1200px | 最佳展示效果         |

## 动画和过渡

### 15. 动画效果

| 动画名称   | 持续时间 | 缓动函数      | 应用场景       |
| ---------- | -------- | ------------- | -------------- |
| 淡入动画   | `0.3s`   | `ease-in-out` | 页面元素加载   |
| 悬停效果   | `0.3s`   | `ease`        | 按钮、卡片悬停 |
| 表格行悬停 | `0.2s`   | `ease`        | 表格行交互     |

## 开发实现建议

### 16. 组件使用优先级

1. **优先使用 Ant Design 组件**: 保证设计一致性和功能完整性
2. **适当自定义样式**: 在 Ant Design 基础上微调以符合设计稿
3. **保持响应式设计**: 确保在不同屏幕尺寸下的良好表现
4. **注意性能优化**: 大数据表格使用虚拟滚动，图表按需加载

### 17. 关键实现点

- **左侧导航**: 使用 `Layout.Sider` + `Menu`，配置深色主题
- **数据表格**: 使用 `Table` 组件，配置分页和排序功能
- **图表集成**: 结合 Chart.js 实现数据可视化
- **模态框交互**: 使用 `Modal` 组件实现车辆详情展示
- **筛选表单**: 使用 `Form` + 各类输入组件实现条件筛选

### 18. 注意事项

- 所有交互元素都应提供适当的反馈
- 表格数据加载时显示 Loading 状态
- 错误状态需要明确的视觉提示和处理建议
- 保持与现有 SaaS 系统的设计语言一致性

---

**说明**: 本对照表基于擎路 SaaS 系统真实页面设计，确保新功能与现有系统的视觉和交互一致性。开发时请严格按照组件库规范实现，保证代码质量和维护性。
