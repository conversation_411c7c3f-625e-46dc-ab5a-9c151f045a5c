<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车型管理 - 擎路PC</title>
    <!-- 引入 Ant Design CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd@5.12.8/dist/reset.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd@5.12.8/dist/antd.min.css">
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, "system-ui", "Segoe UI", Robot<PERSON>, "Helvetica Neue", Aria<PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            font-size: 12px;
            line-height: 18.858px;
            background-color: #f0f2f5;
            color: rgba(0, 0, 0, 0.85);
        }

        /* 布局容器 */
        .app-container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏 */
        .sidebar {
            width: 200px;
            background-color: #001529;
            color: white;
            padding: 16px 0;
        }

        .sidebar-header {
            padding: 0 16px 16px;
            border-bottom: 1px solid #303030;
        }

        .sidebar-menu {
            padding: 16px 0;
        }

        .menu-item {
            padding: 12px 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            transition: background-color 0.3s;
        }

        .menu-item:hover {
            background-color: #1890ff;
        }

        .menu-item.active {
            background-color: #1890ff;
        }

        .menu-item i {
            margin-right: 8px;
            width: 16px;
            height: 16px;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航 */
        .top-nav {
            height: 64px;
            background-color: white;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }

        .breadcrumb {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 页面内容 */
        .page-content {
            flex: 1;
            padding: 24px;
            background-color: #f0f2f5;
        }

        /* 搜索表单 */
        .search-form {
            background-color: white;
            padding: 24px;
            border-radius: 6px;
            margin-bottom: 16px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 16px;
        }

        .form-item {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }

        .form-label {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.85);
            margin-bottom: 4px;
            font-weight: 500;
        }

        .form-input {
            height: 32px;
            padding: 4px 11px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            font-size: 12px;
            transition: all 0.3s;
        }

        .form-input:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .form-select {
            height: 32px;
            padding: 4px 11px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            font-size: 12px;
            background-color: white;
            cursor: pointer;
        }

        .radio-group {
            display: flex;
            gap: 16px;
        }

        .radio-item {
            display: flex;
            align-items: center;
            gap: 4px;
            cursor: pointer;
        }

        .form-buttons {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .btn {
            height: 32px;
            padding: 5.6px 15px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-primary {
            background-color: #4753f4;
            border-color: #4753f4;
            color: white;
        }

        .btn-primary:hover {
            background-color: #3f4bdb;
            border-color: #3f4bdb;
        }

        .btn-default {
            background-color: white;
            color: rgba(0, 0, 0, 0.85);
        }

        .btn-default:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        /* 操作栏 */
        .action-bar {
            background-color: white;
            padding: 16px 24px;
            border-radius: 6px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }

        .info-text {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.65);
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        /* 表格 */
        .table-container {
            background-color: white;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }

        .table th {
            background-color: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            border-bottom: 1px solid #f0f0f0;
        }

        .table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            color: rgba(0, 0, 0, 0.85);
        }

        .table tr:hover {
            background-color: #fafafa;
        }

        .expand-button {
            color: #4753f4;
            cursor: pointer;
            font-size: 12px;
        }

        .action-link {
            color: #4753f4;
            text-decoration: none;
            margin-right: 8px;
            font-size: 12px;
            cursor: pointer;
        }

        .action-link:hover {
            color: #3f4bdb;
        }

        .action-link.danger {
            color: #ff4d4f;
        }

        .action-link.danger:hover {
            color: #ff7875;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 16px;
            background-color: white;
            border-top: 1px solid #f0f0f0;
        }

        .page-item {
            padding: 8px 12px;
            margin: 0 4px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            cursor: pointer;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.85);
            transition: all 0.3s;
        }

        .page-item:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .page-item.active {
            background-color: #1890ff;
            border-color: #1890ff;
            color: white;
        }

        .page-item.disabled {
            color: rgba(0, 0, 0, 0.25);
            cursor: not-allowed;
        }

        /* 改动点标注样式 */
        .change-highlight {
            position: relative;
            border: 2px dashed #ff4d4f !important;
            background-color: rgba(255, 77, 79, 0.1) !important;
        }

        .change-highlight::before {
            content: "新增功能";
            position: absolute;
            top: -8px;
            left: 8px;
            background: #ff4d4f;
            color: white;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 2px;
            z-index: 1000;
        }

        .modify-highlight {
            position: relative;
            border: 2px dashed #fa8c16 !important;
            background-color: rgba(250, 140, 22, 0.1) !important;
        }

        .modify-highlight::before {
            content: "修改功能";
            position: absolute;
            top: -8px;
            left: 8px;
            background: #fa8c16;
            color: white;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 2px;
            z-index: 1000;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .form-row {
                flex-direction: column;
            }
            
            .form-item {
                min-width: 100%;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 80px;
            }
            
            .action-bar {
                flex-direction: column;
                gap: 16px;
                align-items: flex-start;
            }
            
            .table {
                font-size: 11px;
            }
            
            .table th,
            .table td {
                padding: 8px 12px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h3>擎路管理系统</h3>
            </div>
            <div class="sidebar-menu">
                <div class="menu-item">
                    <i>🏠</i>
                    <span>前台</span>
                </div>
                <div class="menu-item">
                    <i>📋</i>
                    <span>订单</span>
                </div>
                <div class="menu-item">
                    <i>📦</i>
                    <span>库存</span>
                </div>
                <div class="menu-item">
                    <i>💰</i>
                    <span>价格</span>
                </div>
                <div class="menu-item">
                    <i>📊</i>
                    <span>数据中心</span>
                </div>
                <div class="menu-item">
                    <i>🏪</i>
                    <span>门店</span>
                </div>
                <div class="menu-item active">
                    <i>🚗</i>
                    <span>车辆</span>
                </div>
                <div class="menu-item">
                    <i>📱</i>
                    <span>ETC管理</span>
                </div>
                <div class="menu-item">
                    <i>🏢</i>
                    <span>商家</span>
                </div>
                <div class="menu-item">
                    <i>📝</i>
                    <span>任务</span>
                </div>
                <div class="menu-item">
                    <i>💳</i>
                    <span>账单</span>
                </div>
                <div class="menu-item">
                    <i>📢</i>
                    <span>营销</span>
                </div>
                <div class="menu-item">
                    <i>🔗</i>
                    <span>渠道</span>
                </div>
                <div class="menu-item">
                    <i>👥</i>
                    <span>用户</span>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航 -->
            <div class="top-nav">
                <div class="breadcrumb">
                    车辆 / 车型管理
                </div>
                <div class="user-info">
                    <span>企鹅123</span>
                    <span>退出</span>
                </div>
            </div>

            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 搜索表单 -->
                <div class="search-form">
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">车型 :</label>
                            <input type="text" class="form-input" placeholder="ID/名称">
                        </div>
                        <div class="form-item">
                            <label class="form-label">品牌 :</label>
                            <input type="text" class="form-input" placeholder="ID/名称">
                        </div>
                        <div class="form-item">
                            <label class="form-label">车型组 :</label>
                            <select class="form-select">
                                <option>请选择</option>
                                <option>经济型</option>
                                <option>舒适型</option>
                                <option>豪华型</option>
                            </select>
                        </div>
                        <div class="form-item">
                            <label class="form-label">牌照类型 :</label>
                            <select class="form-select">
                                <option>请选择</option>
                                <option>普牌</option>
                                <option>京牌</option>
                                <option>沪牌</option>
                                <option>粤牌</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">网络平台 :</label>
                            <select class="form-select">
                                <option>请选择</option>
                                <option>携程</option>
                                <option>飞猪</option>
                                <option>哈啰</option>
                                <option>租租车</option>
                            </select>
                        </div>
                        <div class="form-item">
                            <label class="form-label">平台车型ID :</label>
                            <input type="text" class="form-input" placeholder="请输入平台车型ID">
                        </div>
                        <div class="form-item">
                            <label class="form-label">携程车型状态 :</label>
                            <div class="radio-group">
                                <label class="radio-item">
                                    <input type="radio" name="status" value="success">
                                    <span>成功</span>
                                </label>
                                <label class="radio-item">
                                    <input type="radio" name="status" value="failed">
                                    <span>失败</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="form-buttons">
                        <button class="btn btn-default">重 置</button>
                        <button class="btn btn-primary">查 询</button>
                    </div>
                </div>

                <!-- 操作栏 -->
                <div class="action-bar">
                    <div class="info-text">
                        <span>39个车型</span>
                        <span style="margin-left: 16px;">排序方式：修改时间 近→远</span>
                    </div>
                    <div class="action-buttons">
                        <button class="btn btn-default">车型标签管理</button>
                        <button class="btn btn-default">导 出</button>
                        <button class="btn btn-primary change-highlight">新增车型</button>
                    </div>
                </div>

                <!-- 表格 -->
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>车型</th>
                                <th>车辆信息 <span class="expand-button">展开></span></th>
                                <th>网络平台名称</th>
                                <th>操作</th>
                            </tr>
                            <tr>
                                <th>车牌类型</th>
                                <th>线下渠道</th>
                                <th>携程</th>
                                <th>飞猪</th>
                                <th>哈啰</th>
                                <th>租租车</th>
                                <th>悟空</th>
                                <th>滴滴</th>
                                <th>同程</th>
                                <th>神州</th>
                                <th>铁行</th>
                                <th>摩捷</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>特斯拉 Model Y 2022款 后轮驱动版 2022款</td>
                                <td>粤A</td>
                                <td></td>
                                <td>特斯拉 Model Y 2022款 后轮驱动版</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <a href="#" class="action-link">详情</a>
                                    <a href="#" class="action-link">修改</a>
                                    <a href="#" class="action-link danger">删除</a>
                                </td>
                            </tr>
                            <tr>
                                <td>特斯拉 Model Y 2022款 长续航全轮驱动版 2022款</td>
                                <td>粤B</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <a href="#" class="action-link">详情</a>
                                    <a href="#" class="action-link">修改</a>
                                    <a href="#" class="action-link danger">删除</a>
                                </td>
                            </tr>
                            <tr>
                                <td>沃尔沃 V60 2017款 T4 智尚版 国VI 2017款</td>
                                <td>沪C</td>
                                <td></td>
                                <td>沃尔沃 V60 2017款 T4 智尚版 国VI</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <a href="#" class="action-link">详情</a>
                                    <a href="#" class="action-link">修改</a>
                                    <a href="#" class="action-link danger">删除</a>
                                </td>
                            </tr>
                            <tr>
                                <td>迈莎锐 Cayenne 2021款 3.0T Black Carbon 2021款</td>
                                <td>京牌</td>
                                <td></td>
                                <td>迈莎锐 Cayenne 2021款 3.0T Black Carbon</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <a href="#" class="action-link">详情</a>
                                    <a href="#" class="action-link">修改</a>
                                    <a href="#" class="action-link danger">删除</a>
                                </td>
                            </tr>
                            <tr>
                                <td>本田 凌派 2019款 180Turbo CVT舒适版 国V 2019款</td>
                                <td>普牌</td>
                                <td></td>
                                <td>本田 凌派 2019款 180Turbo CVT舒适版 国V</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <a href="#" class="action-link">详情</a>
                                    <a href="#" class="action-link">修改</a>
                                    <a href="#" class="action-link danger">删除</a>
                                </td>
                            </tr>
                            <tr>
                                <td>大众 速腾 2021款 200TSI DSG超越版 2021款</td>
                                <td>普牌</td>
                                <td></td>
                                <td>大众 速腾 2021款 200TSI DSG超越版</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <a href="#" class="action-link">详情</a>
                                    <a href="#" class="action-link">修改</a>
                                    <a href="#" class="action-link danger">删除</a>
                                </td>
                            </tr>
                            <tr>
                                <td>大众 迈腾GTE插电混动 2020款 GTE 豪华型 2020款</td>
                                <td>普牌</td>
                                <td></td>
                                <td>大众 迈腾GTE插电混动 2020款 GTE 豪华型</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <a href="#" class="action-link">详情</a>
                                    <a href="#" class="action-link">修改</a>
                                    <a href="#" class="action-link danger">删除</a>
                                </td>
                            </tr>
                            <tr>
                                <td>特斯拉 Model Y 2022款 后轮驱动版 2022款</td>
                                <td>普牌</td>
                                <td></td>
                                <td>特斯拉 Model Y 2022款 后轮驱动版</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <a href="#" class="action-link">详情</a>
                                    <a href="#" class="action-link">修改</a>
                                    <a href="#" class="action-link danger">删除</a>
                                </td>
                            </tr>
                            <tr>
                                <td>Polestar极星 Polestar 2 2021款 双电机长续航 2021款</td>
                                <td>普牌</td>
                                <td></td>
                                <td>Polestar极星 Polestar 2 2021款 双电机长续航</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <a href="#" class="action-link">详情</a>
                                    <a href="#" class="action-link">修改</a>
                                    <a href="#" class="action-link danger">删除</a>
                                </td>
                            </tr>
                            <tr>
                                <td>广汽传祺 传祺M8 2021款 领秀系列 390T 尊贵版 2021款</td>
                                <td>普牌</td>
                                <td></td>
                                <td>广汽传祺 传祺M8 2021款 领秀系列 390T 尊贵版</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <a href="#" class="action-link">详情</a>
                                    <a href="#" class="action-link">修改</a>
                                    <a href="#" class="action-link danger">删除</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- 分页 -->
                    <div class="pagination">
                        <div class="page-item disabled">上一页</div>
                        <div class="page-item active">1</div>
                        <div class="page-item">2</div>
                        <div class="page-item">3</div>
                        <div class="page-item">4</div>
                        <div class="page-item">下一页</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Mock数据定义
        const mockData = {
            vehicleModels: [
                {
                    id: 1,
                    name: "特斯拉 Model Y 2022款 后轮驱动版 2022款",
                    licenseType: "粤A",
                    brandName: "特斯拉",
                    seryName: "Model Y",
                    yearStyle: "2022款",
                    modelGroupName: "豪华型",
                    channelBindList: [
                        { channelId: 2, bindChannelVehicleName: "特斯拉 Model Y 2022款 后轮驱动版", synced: 1 }
                    ]
                },
                {
                    id: 2,
                    name: "特斯拉 Model Y 2022款 长续航全轮驱动版 2022款",
                    licenseType: "粤B",
                    brandName: "特斯拉",
                    seryName: "Model Y",
                    yearStyle: "2022款",
                    modelGroupName: "豪华型",
                    channelBindList: []
                },
                {
                    id: 3,
                    name: "沃尔沃 V60 2017款 T4 智尚版 国VI 2017款",
                    licenseType: "沪C",
                    brandName: "沃尔沃",
                    seryName: "V60",
                    yearStyle: "2017款",
                    modelGroupName: "舒适型",
                    channelBindList: [
                        { channelId: 2, bindChannelVehicleName: "沃尔沃 V60 2017款 T4 智尚版 国VI", synced: 1 }
                    ]
                }
            ],
            totalCount: 39,
            currentPage: 1,
            pageSize: 10
        };

        // 改动点标注逻辑
        function highlightChanges() {
            // 标注新增功能
            const addButton = document.querySelector('.btn-primary.change-highlight');
            if (addButton) {
                addButton.style.position = 'relative';
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            highlightChanges();
            
            // 添加交互效果
            const menuItems = document.querySelectorAll('.menu-item');
            menuItems.forEach(item => {
                item.addEventListener('click', function() {
                    menuItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 表格行点击效果
            const tableRows = document.querySelectorAll('.table tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('click', function() {
                    tableRows.forEach(r => r.style.backgroundColor = '');
                    this.style.backgroundColor = '#f0f8ff';
                });
            });

            // 搜索表单提交
            const searchForm = document.querySelector('.search-form');
            const searchButton = searchForm.querySelector('.btn-primary');
            searchButton.addEventListener('click', function() {
                alert('执行搜索功能');
            });

            // 重置按钮
            const resetButton = searchForm.querySelector('.btn-default');
            resetButton.addEventListener('click', function() {
                const inputs = searchForm.querySelectorAll('input, select');
                inputs.forEach(input => {
                    if (input.type === 'radio') {
                        input.checked = false;
                    } else {
                        input.value = '';
                    }
                });
            });

            // 新增车型按钮
            const addButton = document.querySelector('.btn-primary.change-highlight');
            addButton.addEventListener('click', function() {
                alert('跳转到新增车型页面');
            });

            // 操作按钮事件
            const actionLinks = document.querySelectorAll('.action-link');
            actionLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const action = this.textContent;
                    const row = this.closest('tr');
                    const modelName = row.cells[0].textContent;
                    
                    switch(action) {
                        case '详情':
                            alert(`查看车型详情：${modelName}`);
                            break;
                        case '修改':
                            alert(`修改车型：${modelName}`);
                            break;
                        case '删除':
                            if (confirm(`确定要删除车型：${modelName} 吗？`)) {
                                alert('删除成功');
                            }
                            break;
                    }
                });
            });

            // 分页点击事件
            const pageItems = document.querySelectorAll('.page-item');
            pageItems.forEach(item => {
                item.addEventListener('click', function() {
                    if (!this.classList.contains('disabled')) {
                        pageItems.forEach(i => i.classList.remove('active'));
                        this.classList.add('active');
                        alert(`跳转到第 ${this.textContent} 页`);
                    }
                });
            });

            // 展开按钮点击事件
            const expandButton = document.querySelector('.expand-button');
            expandButton.addEventListener('click', function() {
                alert('展开车辆详细信息');
            });
        });

        // 模拟API调用
        function fetchVehicleModels(params) {
            return new Promise((resolve) => {
                setTimeout(() => {
                    resolve({
                        success: true,
                        data: {
                            list: mockData.vehicleModels,
                            count: mockData.totalCount,
                            final: false
                        }
                    });
                }, 500);
            });
        }

        // 模拟删除操作
        async function removeVehicleModel(id) {
            try {
                const response = await fetch(`/api/vehicle-models/${id}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + localStorage.getItem('token')
                    }
                });
                return response.json();
            } catch (error) {
                console.error('删除车型失败:', error);
                return { success: false, error: { message: '网络错误' } };
            }
        }
    </script>
</body>
</html> 