# H5取消时间功能变更文档

## 1. 功能概述

### 1.1 需求描述
在H5短租订单详情页面中，为已取消的订单添加"取消时间"显示功能，让用户能够清楚地看到订单的取消时间。基于100%像素级精确的原型设计，确保新功能与现有界面完美融合。

### 1.2 功能目标
- 提升用户体验，让用户了解订单取消的具体时间
- 保持与现有设计风格的100%一致性
- 仅在已取消订单中显示，避免信息冗余
- 确保移动端适配和响应式设计

### 1.3 原型参考
- **原型文件**：`prd/v1.0.5/h5/H5取消时间原型视觉稿.html`
- **设计标准**：100%像素级精确复现线上订单详情页面
- **参考订单**：线上订单号1706192（已取消状态）

## 2. 技术实现方案

### 2.1 前端实现

#### 2.1.1 文件修改清单
```
modules/h5/views/order/detail.vue                    # 主要视图文件
modules/h5/comp-lib/qinglu-vant/src/services/models/OrderDetailModels/OrderDetailInfoModel.js  # 数据模型
```

#### 2.1.2 数据模型扩展
**文件：** `OrderDetailInfoModel.js`

**新增字段：**
```javascript
// 在OrderDetailInfoModel类中添加
cancelTimeStr = {
  type: String,
  field: (data) => {
    // 仅在订单状态为已取消时返回格式化时间
    if (data.status === 'CANCELLED' && data.cancelTime) {
      return Tempo.format(data.cancelTime);
    }
    return null;
  }
}
```

**说明：**
- 使用现有的Tempo工具进行时间格式化
- 默认格式为'yyyy-MM-dd hh:mm'（如：2025-07-31 15:45）
- 仅在订单状态为已取消且存在cancelTime时返回值
- 与现有orderTimeStr字段保持相同的实现模式

#### 2.1.3 视图层修改
**文件：** `detail.vue`

**模板结构修改：**
```vue
<template>
  <!-- 订单信息列表 - 精确按照原型布局 -->
  <ul class="order-info-list">
    <li class="order-info-item">
      <span class="order-info-label">下单：</span>
      <span class="order-info-value">{{ orderInfo.orderTimeStr }}</span>
    </li>
    <li class="order-info-item">
      <span class="order-info-label">用车：</span>
      <span class="order-info-value">{{ orderInfo.useTimeStr }}</span>
    </li>

    <!-- 新增：取消时间显示 - 精确位置和样式 -->
    <li
      v-if="orderInfo.cancelTimeStr"
      class="order-info-item"
    >
      <span class="order-info-label">取消：</span>
      <span class="order-info-value">{{ orderInfo.cancelTimeStr }}</span>
    </li>

    <li class="order-info-item">
      <span class="order-info-label">订单号：</span>
      <span class="order-info-value">{{ orderInfo.orderNo }}</span>
      <button class="copy-btn" @click="copyOrderNo">复制</button>
    </li>
    <li class="order-info-item">
      <span class="order-info-label">渠道订单号：</span>
      <span class="order-info-value">{{ orderInfo.channelOrderNo }}</span>
      <button class="copy-btn" @click="copyChannelOrderNo">复制</button>
    </li>
  </ul>
</template>
```

**样式规范（100%像素级精确）：**
```vue
<style scoped>
/* 订单信息列表 - 精确复现线上样式 */
.order-info-list {
  list-style: none;
  margin-bottom: 16px;
}

.order-info-item {
  display: flex;
  align-items: flex-start;
  padding: 8px 0;
  font-size: 14px;
  line-height: 20px;
}

.order-info-label {
  color: #323233;
  min-width: auto;
  margin-right: 4px;
  flex-shrink: 0;
}

.order-info-value {
  flex: 1;
  color: #323233;
  word-break: break-all;
}

.copy-btn {
  background: transparent;
  border: 1px solid #1989fa;
  color: #1989fa;
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 10px;
  margin-left: 8px;
  cursor: pointer;
  outline: none;
  flex-shrink: 0;
}

/* 承租人信息模块 - 100%精确复现线上布局 */
.tenant-section {
  background-color: #fff;
  padding: 16px;
  margin-bottom: 8px;
}

.tenant-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.tenant-content {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.tenant-row {
  display: flex;
  align-items: flex-start;
  padding-bottom: 8px;
}

.tenant-row:last-child {
  padding-bottom: 0;
}

.tenant-label {
  color: #323233;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  min-width: 60px;
  margin-right: 8px;
  flex-shrink: 0;
}

.tenant-value {
  color: #323233;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  word-break: break-all;
  flex: 1;
}
</style>
```

### 2.2 后端API扩展

#### 2.2.1 API响应字段
**接口：** 订单详情API

**新增响应字段：**
```json
{
  "data": {
    "orderNo": "1706192",
    "status": "CANCELLED",
    "orderTime": 1722408600,
    "cancelTime": 1722416700,  // 新增：取消时间戳（秒）
    // ... 其他现有字段
  }
}
```

**字段说明：**
- `cancelTime`: 订单取消时间戳，单位为秒
- 仅在订单状态为已取消时返回此字段
- 时间戳格式便于前端进行时区转换和格式化

## 3. 设计规范（100%像素级精确）

### 3.1 显示位置
- **精确位置**：订单信息列表中，"用车"时间字段之后，"订单号"字段之前
- **显示顺序**：下单 → 用车 → **取消** → 订单号 → 渠道订单号
- **布局结构**：使用与现有字段完全相同的HTML结构和CSS类名

### 3.2 显示格式
- **标签文本**：`取消：`（与"下单："、"用车："保持一致的格式）
- **时间格式**：`yyyy-MM-dd hh:mm`
- **显示示例**：`取消：2025-07-31 15:45`
- **字符间距**：标签与时间之间无额外间距

### 3.3 显示条件
- 仅在订单状态为"已取消"（CANCELLED）时显示
- 必须存在有效的cancelTime数据（非空且大于0）
- 使用v-if指令进行条件渲染，避免空白占位

### 3.4 样式规范（精确到像素）
- **字体大小**：14px（与现有信息字段完全一致）
- **行高**：20px（确保垂直对齐）
- **颜色规范**：
  - 标签颜色：#323233（与现有标签保持一致）
  - 内容颜色：#323233（与现有内容保持一致）
- **布局规范**：
  - 上下内边距：8px 0
  - 左右对齐：flex布局，标签左对齐，内容右对齐
  - 标签最小宽度：auto（自适应内容宽度）
  - 标签右边距：4px

### 3.5 响应式适配
- **移动端适配**：375px宽度下完美显示
- **文字换行**：长文本自动换行，保持布局稳定
- **触摸友好**：保持与其他字段相同的触摸区域大小

## 4. 测试要点

### 4.1 功能测试
- [ ] **核心功能验证**
  - [ ] 已取消订单正确显示取消时间
  - [ ] 非取消订单不显示取消时间字段
  - [ ] 时间格式严格按照yyyy-MM-dd hh:mm显示
  - [ ] cancelTime为空或null时不显示字段
  - [ ] 取消时间显示在正确位置（用车时间之后）

- [ ] **数据处理验证**
  - [ ] Tempo.format()正确格式化时间戳
  - [ ] 时区转换正确（本地时间显示）
  - [ ] 边界值测试（最小/最大时间戳）

### 4.2 UI/UX测试
- [ ] **像素级精确验证**
  - [ ] 字体大小、颜色与原型完全一致
  - [ ] 行间距、内边距精确匹配
  - [ ] 与现有时间字段样式100%一致
  - [ ] 标签对齐、内容对齐正确
  - [ ] 承租人信息模块垂直布局正确
  - [ ] 每行标签与内容的flex布局精确

- [ ] **响应式测试**
  - [ ] 375px宽度下完美显示
  - [ ] 不同内容长度下布局稳定
  - [ ] 长时间文本正确换行
  - [ ] 承租人信息字段自适应宽度

### 4.3 兼容性测试
- [ ] **浏览器兼容性**
  - [ ] iOS Safari（iOS 12+）
  - [ ] Android Chrome（Chrome 70+）
  - [ ] 微信内置浏览器
  - [ ] QQ浏览器、UC浏览器

- [ ] **设备适配**
  - [ ] iPhone SE (375px)
  - [ ] iPhone 12 (390px)
  - [ ] Android主流机型
  - [ ] 横屏模式适配

### 4.4 性能测试
- [ ] 页面加载时间无明显增加（<100ms差异）
- [ ] 内存使用无异常增长
- [ ] 时间格式化性能正常（<1ms）
- [ ] 大量订单列表滚动流畅

## 5. 开发实施计划

### 5.1 开发阶段（总计4天）
1. **后端API扩展**（1天）
   - 订单详情接口添加cancelTime字段
   - 数据库查询优化
   - API文档更新

2. **前端数据模型修改**（0.5天）
   - OrderDetailInfoModel添加cancelTimeStr字段
   - 单元测试编写

3. **前端视图层实现**（1天）
   - detail.vue模板修改
   - CSS样式精确实现（包括承租人信息模块布局优化）
   - 条件渲染逻辑
   - 像素级精确验证与调整

4. **联调测试**（1天）
   - 前后端接口联调
   - 功能完整性验证
   - 问题修复

5. **代码审查**（0.5天）
   - 代码质量检查
   - 性能优化建议
   - 安全性审查

### 5.2 测试阶段（总计2.5天）
1. **功能测试**（1天）
2. **UI/UX测试**（0.5天）
3. **兼容性测试**（0.5天）
4. **回归测试**（0.5天）

### 5.3 发布阶段（总计1天）
1. **预发布环境验证**（0.5天）
2. **生产环境发布**（0.5天）

**总计开发周期：7.5天**

## 6. 风险评估与应对

### 6.1 技术风险
- **风险等级**：低
- **风险点**：
  - 时间格式化在不同时区的表现
  - 与现有Vant组件的样式冲突
- **应对措施**：
  - 充分测试不同时区场景
  - 使用原型作为标准进行像素级对比
  - 重点验证承租人信息模块的布局一致性

### 6.2 业务风险
- **风险等级**：极低
- **风险点**：用户对新增信息的接受度
- **应对措施**：纯展示功能，不影响现有操作流程

### 6.3 用户体验风险
- **风险等级**：低
- **风险点**：信息过载或布局混乱
- **应对措施**：严格按照原型实现，保持设计一致性

## 7. 验收标准

### 7.1 功能验收
- ✅ 已取消订单正确显示取消时间
- ✅ 时间格式严格符合yyyy-MM-dd hh:mm规范
- ✅ 显示位置精确匹配原型设计
- ✅ 非取消订单不显示该字段
- ✅ 数据为空时正确隐藏

### 7.2 设计验收
- ✅ 与原型100%像素级一致
- ✅ 字体、颜色、间距完全匹配
- ✅ 响应式布局正常工作
- ✅ 与现有字段样式完全统一
- ✅ 承租人信息模块垂直布局精确匹配线上样式
- ✅ 所有模块间距和对齐方式与线上完全一致

### 7.3 性能验收
- ✅ 页面加载时间增加<100ms
- ✅ 内存使用无异常增长
- ✅ 滚动性能无影响

### 7.4 兼容性验收
- ✅ 主流移动端浏览器100%兼容
- ✅ 不同屏幕尺寸完美适配
- ✅ 微信等内置浏览器正常显示

---

**文档版本：** v2.1
**创建日期：** 2025-07-31
**最后更新：** 2025-07-31
**负责人：** 产品开发团队
**原型参考：** `prd/v1.0.5/h5/H5取消时间原型视觉稿.html`
**更新说明：** 基于线上页面分析，精确修正了承租人信息模块的布局实现
