# 擎路 AI - 知识库存储全局提示词

## 🎯 核心目标

当产品经理使用全局提示词生成的视觉稿HTML满足产品要求后，自动调用outline mcp将最终产物存储到知识库中，为后续研发提供完整的开发参考资料。

## 📋 存储触发条件

### 自动触发条件
1. **视觉稿确认完成**：产品经理明确表示视觉稿满足要求
2. **质量检查通过**：视觉稿通过所有质量检查清单项目
3. **用户明确指示**：用户明确要求存储到知识库

### 触发关键词识别
- "视觉稿满足要求"
- "可以存储到知识库"
- "保存到知识库"
- "存入知识库"
- "满足产品要求"
- "通过质量检查"
- "可以用于开发"

## 🗂️ 存储内容清单

### 必需存储内容
1. **HTML原型视觉稿**：完整的页面HTML文件，包含改动点标注
2. **功能需求摘要**：从原始需求中提取的核心功能点
3. **技术实现要点**：关键的技术实现细节和注意事项
4. **变更说明文档**：详细的功能变更和实现细节

### 可选存储内容（根据生成情况）
1. **PRD文档**：如果生成了PRD文档
2. **组件对照表**：如果生成了组件对照表
3. **设计规范说明**：相关的设计规范和样式指南

## 📝 知识库存储格式

### 存储结构模板
```markdown
# [功能名称] - 开发参考文档

## 📊 项目信息
- **功能名称**：[功能名称]
- **版本号**：[版本号]
- **平台**：[H5/PC]
- **创建时间**：[YYYY-MM-DD HH:mm:ss]
- **产品经理**：[产品经理姓名]
- **状态**：已确认，可用于开发

## 🎯 功能概述
[从原始需求中提取的功能描述]

## 📋 核心需求
[核心功能点列表]

## 🎨 视觉稿文件
- **文件路径**：`prd/v[版本号]/[平台]/[功能名称]-原型图-v[版本号].html`
- **文件大小**：[文件大小]
- **最后修改**：[修改时间]

## 🔧 技术实现要点
[关键技术实现细节]

## 📝 变更说明
[功能变更和实现细节]

## 🎯 开发提示词生成指南
基于此视觉稿和变更说明文档，研发可使用以下模板生成开发提示词：

### 前端开发提示词模板
```
基于视觉稿：prd/v[版本号]/[平台]/[功能名称]-原型图-v[版本号].html
实现功能：[功能名称]
技术栈：[技术栈信息]
关键要求：
1. 严格按照视觉稿实现UI界面
2. 注意改动点标注的新增和修改功能
3. [其他技术要求]
```

## 📎 相关文档
[如果有其他相关文档的链接]

---
*此文档由擎路AI自动生成，用于指导研发团队进行功能开发*
```

## 🤖 自动存储执行流程

### 1. 检测触发条件
```javascript
// 伪代码：检测触发条件
function detectStorageTrigger(userMessage) {
  const triggerKeywords = [
    "视觉稿满足要求",
    "可以存储到知识库", 
    "保存到知识库",
    "存入知识库",
    "满足产品要求",
    "通过质量检查",
    "可以用于开发"
  ];
  
  return triggerKeywords.some(keyword => 
    userMessage.includes(keyword)
  );
}
```

### 2. 收集存储内容
- 获取最新生成的HTML视觉稿文件路径
- 提取原始需求中的核心信息
- 整理技术实现要点
- 收集相关文档链接

### 3. 格式化存储内容
- 按照知识库存储格式模板组织内容
- 确保所有必需字段都已填充
- 验证文件路径和链接的有效性

### 4. 调用outline mcp存储
```javascript
// 伪代码：调用outline mcp
async function storeToKnowledgeBase(content) {
  try {
    const result = await outlineMcp.store({
      title: `${功能名称} - 开发参考文档`,
      content: content,
      tags: ["开发参考", "视觉稿", "产品需求"],
      category: "产品开发"
    });
    
    return result;
  } catch (error) {
    console.error("存储到知识库失败:", error);
    throw error;
  }
}
```

## 📋 存储质量检查

### 存储前检查清单
- [ ] HTML视觉稿文件存在且可访问
- [ ] 功能需求信息完整
- [ ] 技术实现要点明确
- [ ] 文件路径正确
- [ ] 版本号信息准确
- [ ] 创建时间和产品经理信息完整

### 存储后验证
- [ ] 知识库中成功创建文档
- [ ] 文档内容完整可读
- [ ] 相关链接可正常访问
- [ ] 标签和分类正确设置

## 🔄 自动执行示例

### 触发场景
```
用户：这个视觉稿满足产品要求，可以存储到知识库中了
```

### 自动执行步骤
1. **检测触发**：识别到"满足产品要求"和"存储到知识库"关键词
2. **收集信息**：
   - 视觉稿文件：`prd/v1.0.4/pc/车辆资源检测功能-原型图-v1.0.4.html`
   - 功能名称：车辆资源检测功能
   - 版本号：v1.0.4
   - 平台：PC
3. **格式化内容**：按照模板组织存储内容
4. **调用存储**：使用outline mcp存储到知识库
5. **确认反馈**：向用户确认存储成功

### 执行反馈
```
✅ 已成功将"车辆资源检测功能"相关文档存储到知识库

📋 存储内容：
- HTML原型视觉稿：prd/v1.0.4/pc/车辆资源检测功能-原型图-v1.0.4.html
- 功能需求摘要：车辆资源检测功能核心需求
- 技术实现要点：关键技术细节和注意事项
- 变更说明文档：详细的功能变更记录

🎯 后续使用：
研发团队可根据知识库中的文档和视觉稿生成开发提示词进行功能开发。
```

## ⚙️ 配置参数

### outline mcp 配置
```json
{
  "service": "outline-mcp",
  "endpoint": "[outline服务地址]",
  "authentication": {
    "type": "token",
    "token": "[访问令牌]"
  },
  "defaultTags": ["开发参考", "视觉稿", "产品需求"],
  "defaultCategory": "产品开发"
}
```

### 存储规则配置
```json
{
  "autoTrigger": true,
  "triggerKeywords": [
    "视觉稿满足要求",
    "可以存储到知识库",
    "保存到知识库", 
    "存入知识库",
    "满足产品要求",
    "通过质量检查",
    "可以用于开发"
  ],
  "requiredFields": [
    "功能名称",
    "版本号", 
    "平台",
    "HTML文件路径"
  ],
  "qualityCheck": true
}
```

## 🚨 异常处理

### 常见异常情况
1. **HTML文件不存在**：提示用户先生成视觉稿
2. **outline mcp服务不可用**：提示稍后重试或手动存储
3. **必需信息缺失**：要求用户补充缺失信息
4. **存储权限不足**：提示检查访问权限配置

### 异常处理流程
```javascript
// 伪代码：异常处理
async function handleStorageError(error, context) {
  switch(error.type) {
    case 'FILE_NOT_FOUND':
      return "❌ 未找到HTML视觉稿文件，请先使用全局提示词生成视觉稿";
    
    case 'SERVICE_UNAVAILABLE':
      return "❌ 知识库服务暂时不可用，请稍后重试或联系管理员";
    
    case 'MISSING_REQUIRED_INFO':
      return `❌ 缺少必需信息：${error.missingFields.join(', ')}，请补充后重试`;
    
    case 'PERMISSION_DENIED':
      return "❌ 存储权限不足，请检查outline mcp访问权限配置";
    
    default:
      return `❌ 存储失败：${error.message}，请联系技术支持`;
  }
}
```

## 📈 使用统计和优化

### 统计指标
- 自动存储触发次数
- 存储成功率
- 平均存储时间
- 用户满意度反馈

### 持续优化
- 根据使用反馈优化触发关键词
- 完善存储内容模板
- 提升存储成功率
- 优化异常处理机制

---

**注意**：此提示词与global.md配合使用，确保产品开发流程的完整性和连续性。当视觉稿满足要求时，自动触发知识库存储，为研发团队提供完整的开发参考资料。
