<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车辆资源检测 - 擎路SaaS管理系统</title>
    
    <!-- Ant Design CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd@5.12.0/dist/reset.css">
    <script src="https://cdn.jsdelivr.net/npm/antd@5.12.0/dist/antd.min.js"></script>
    
    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@ant-design/icons@5.2.6/lib/index.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f0f2f5;
            color: #000000d9;
            line-height: 1.5715;
        }

        /* 基于真实SaaS系统的布局结构 */
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        /* 左侧导航 - 复刻真实SaaS系统样式 */
        .sidebar {
            width: 200px;
            background: #001529;
            color: #fff;
            position: fixed;
            left: 0;
            top: 0;
            bottom: 0;
            overflow-y: auto;
        }

        .sidebar .logo {
            height: 64px;
            background: #002140;
            display: flex;
            align-items: center;
            padding: 0 24px;
            color: #fff;
            font-weight: 600;
            font-size: 18px;
        }

        .sidebar .nav-menu {
            padding: 16px 0;
        }

        .sidebar .menu-item {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: rgba(255, 255, 255, 0.65);
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }

        .sidebar .menu-item:hover {
            background: #1890ff;
            color: #fff;
        }

        .sidebar .menu-item.active {
            background: #1890ff;
            color: #fff;
        }

        .sidebar .menu-item .icon {
            margin-right: 12px;
            font-size: 16px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 200px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* 顶部区域 */
        .header {
            background: #fff;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            position: relative;
            z-index: 10;
        }

        .breadcrumb {
            color: rgba(0, 0, 0, 0.45);
            font-size: 14px;
        }

        .user-info {
            color: rgba(0, 0, 0, 0.65);
            font-size: 14px;
        }

        /* 页面内容区域 */
        .page-content {
            flex: 1;
            padding: 24px;
            background: #f0f2f5;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 20px;
            font-weight: 500;
            color: #000000d9;
            margin-bottom: 8px;
        }

        .page-description {
            color: #00000073;
            font-size: 14px;
        }

        /* 内容卡片 */
        .content-card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            margin-bottom: 24px;
        }

        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            font-weight: 500;
            font-size: 16px;
            color: #000000d9;
        }

        .card-body {
            padding: 24px;
        }

        /* 筛选表单 */
        .filter-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 16px;
        }

        .form-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .form-label {
            font-size: 14px;
            color: #000000d9;
            font-weight: 500;
        }

        .form-input, .form-select {
            height: 36px;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .form-input:focus, .form-select:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            transition: all 0.3s;
        }

        .stat-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .stat-title {
            font-size: 14px;
            color: #00000073;
        }

        .stat-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .stat-value {
            font-size: 28px;
            font-weight: 600;
            color: #000000d9;
            margin-bottom: 4px;
        }

        .stat-trend {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .trend-up {
            color: #52c41a;
        }

        .trend-down {
            color: #ff4d4f;
        }

        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .data-table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: #000000d9;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            color: #000000d9;
        }

        .data-table tr:hover {
            background: #fafafa;
        }

        /* 状态标签 */
        .status-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-normal {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .status-error {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }

        .status-warning {
            background: #fffbe6;
            color: #faad14;
            border: 1px solid #ffe58f;
        }

        /* 按钮样式 */
        .btn {
            padding: 6px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: #fff;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .btn-primary {
            background: #1890ff;
            border-color: #1890ff;
            color: #fff;
        }

        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }

        .btn-danger {
            background: #ff4d4f;
            border-color: #ff4d4f;
            color: #fff;
        }

        .btn-danger:hover {
            background: #ff7875;
            border-color: #ff7875;
        }

        .btn:hover {
            border-color: #40a9ff;
            color: #40a9ff;
        }

        /* 图表容器 */
        .chart-container {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 24px;
            margin-bottom: 24px;
        }

        .chart-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .chart-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 16px;
            color: #000000d9;
        }

        .chart-canvas {
            height: 300px;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 24px;
        }

        .pagination-info {
            color: #00000073;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
        }

        .page-btn {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: #fff;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .page-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .page-btn.active {
            background: #1890ff;
            border-color: #1890ff;
            color: #fff;
        }

        /* 操作栏 */
        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            flex-wrap: wrap;
            gap: 12px;
        }

        .action-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .action-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* 切换标签 */
        .tab-nav {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            margin-bottom: 16px;
        }

        .tab-item {
            padding: 12px 16px;
            cursor: pointer;
            color: #00000073;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab-item.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
        }

        .tab-item:hover {
            color: #1890ff;
        }

        /* 搜索框 */
        .search-input {
            width: 200px;
            position: relative;
        }

        .search-input input {
            width: 100%;
            padding: 8px 12px 8px 36px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
        }

        .search-input .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #00000040;
        }

        /* 模态框 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.45);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: #fff;
            border-radius: 8px;
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 16px;
            font-weight: 500;
        }

        .modal-close {
            cursor: pointer;
            color: #00000073;
            font-size: 16px;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s;
            }

            .main-content {
                margin-left: 0;
            }

            .filter-form {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .chart-container {
                grid-template-columns: 1fr;
            }

            .action-bar {
                flex-direction: column;
                align-items: stretch;
            }
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f0f0f0;
        }

        ::-webkit-scrollbar-thumb {
            background: #bfbfbf;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #999;
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 图例样式 */
        .chart-legend {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            margin-top: 16px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }
    </style>
</head>

<body>
    <div class="admin-layout">
        <!-- 左侧导航栏 - 复刻真实SaaS系统样式 -->
        <div class="sidebar">
            <div class="logo">
                🚗 擎路SaaS
            </div>
            <nav class="nav-menu">
                <div class="menu-item">
                    <span class="icon">🏠</span>
                    <span>前台</span>
                </div>
                <div class="menu-item">
                    <span class="icon">📋</span>
                    <span>订单</span>
                </div>
                <div class="menu-item">
                    <span class="icon">📦</span>
                    <span>库存</span>
                </div>
                <div class="menu-item">
                    <span class="icon">💰</span>
                    <span>价格</span>
                </div>
                <div class="menu-item">
                    <span class="icon">🏪</span>
                    <span>门店</span>
                </div>
                <div class="menu-item active">
                    <span class="icon">🚙</span>
                    <span>车型</span>
                </div>
                <div class="menu-item">
                    <span class="icon">🏢</span>
                    <span>商户</span>
                </div>
                <div class="menu-item">
                    <span class="icon">👥</span>
                    <span>用户</span>
                </div>
            </nav>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 顶部头部 -->
            <header class="header">
                <div class="breadcrumb">
                    车型 / 车辆资源检测
                </div>
                <div class="user-info">
                    企鹅123 退出
                </div>
            </header>

            <!-- 页面内容 -->
            <main class="page-content">
                <!-- 页面标题 -->
                <div class="page-header fade-in">
                    <h1 class="page-title">车辆资源检测</h1>
                    <p class="page-description">快速检测您的车辆资源是否正常展示，排查展示异常问题</p>
                </div>

                <!-- 筛选条件卡片 -->
                <div class="content-card fade-in">
                    <div class="card-header">
                        🔍 筛选条件
                    </div>
                    <div class="card-body">
                        <form class="filter-form">
                            <div class="form-item">
                                <label class="form-label">渠道</label>
                                <select class="form-select">
                                    <option value="">全部渠道</option>
                                    <option value="channel1">某旅行平台</option>
                                    <option value="channel2">某打车平台</option>
                                    <option value="channel3">某租车平台</option>
                                </select>
                            </div>
                            <div class="form-item">
                                <label class="form-label">取车门店</label>
                                <select class="form-select">
                                    <option value="">全部门店</option>
                                    <option value="store1">北京首都机场店</option>
                                    <option value="store2">上海虹桥机场店</option>
                                    <option value="store3">广州白云机场店</option>
                                </select>
                            </div>
                            <div class="form-item">
                                <label class="form-label">还车门店</label>
                                <select class="form-select">
                                    <option value="">全部门店</option>
                                    <option value="store1">北京首都机场店</option>
                                    <option value="store2">上海虹桥机场店</option>
                                    <option value="store3">广州白云机场店</option>
                                </select>
                            </div>
                            <div class="form-item">
                                <label class="form-label">取车时间</label>
                                <input type="date" class="form-input">
                            </div>
                            <div class="form-item">
                                <label class="form-label">还车时间</label>
                                <input type="date" class="form-input">
                            </div>
                            <div class="form-item">
                                <label class="form-label">车型</label>
                                <select class="form-select">
                                    <option value="">全部车型</option>
                                    <option value="type1">经济型</option>
                                    <option value="type2">舒适型</option>
                                    <option value="type3">豪华型</option>
                                    <option value="type4">SUV</option>
                                </select>
                            </div>
                        </form>
                        
                        <div class="action-bar">
                            <div class="action-left">
                                <label>
                                    <input type="checkbox" style="margin-right: 8px;">
                                    仅显示异常车辆
                                </label>
                                <button class="btn" style="color: #1890ff;">
                                    🔧 高级筛选
                                </button>
                            </div>
                            <div class="action-right">
                                <button class="btn">
                                    🔄 重置
                                </button>
                                <button class="btn btn-primary">
                                    🔍 查询
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计概览 -->
                <div class="stats-grid fade-in">
                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-title">车辆总数</span>
                            <div class="stat-icon" style="background: rgba(24, 144, 255, 0.1); color: #1890ff;">
                                🚗
                            </div>
                        </div>
                        <div class="stat-value">128</div>
                        <div class="stat-trend trend-up">
                            ↗ 12% 较上周增加 14 辆
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-title">正常车辆</span>
                            <div class="stat-icon" style="background: rgba(82, 196, 26, 0.1); color: #52c41a;">
                                ✅
                            </div>
                        </div>
                        <div class="stat-value">102</div>
                        <div class="stat-trend trend-up">
                            ↗ 8% 占比 79.7%
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-title">异常车辆</span>
                            <div class="stat-icon" style="background: rgba(255, 77, 79, 0.1); color: #ff4d4f;">
                                ⚠️
                            </div>
                        </div>
                        <div class="stat-value">26</div>
                        <div class="stat-trend trend-up" style="color: #ff4d4f;">
                            ↗ 23% 占比 20.3%
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-title">检测成功率</span>
                            <div class="stat-icon" style="background: rgba(250, 173, 20, 0.1); color: #faad14;">
                                📊
                            </div>
                        </div>
                        <div class="stat-value">96.2%</div>
                        <div class="stat-trend trend-down">
                            ↘ 1.3% 较上周下降 1.3%
                        </div>
                    </div>
                </div>

                <!-- 车辆列表卡片 -->
                <div class="content-card fade-in">
                    <div class="card-header">
                        📋 车辆列表
                    </div>
                    <div class="card-body">
                        <!-- 标签页导航 -->
                        <div class="tab-nav">
                            <div class="tab-item active">全部车辆</div>
                            <div class="tab-item">正常车辆</div>
                            <div class="tab-item">异常车辆</div>
                        </div>

                        <!-- 操作栏 -->
                        <div class="action-bar">
                            <div class="action-left">
                                <span style="color: #00000073; font-size: 14px;">
                                    共 <strong style="color: #1890ff;">128</strong> 条记录
                                </span>
                                <select style="padding: 4px 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                    <option>每页 10 条</option>
                                    <option>每页 20 条</option>
                                    <option>每页 50 条</option>
                                </select>
                            </div>
                            <div class="action-right">
                                <button class="btn">
                                    📥 导出
                                </button>
                                <button class="btn">
                                    🔄 刷新
                                </button>
                                <div class="search-input">
                                    <span class="search-icon">🔍</span>
                                    <input type="text" placeholder="搜索车辆信息...">
                                </div>
                            </div>
                        </div>

                        <!-- 车辆列表表格 -->
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>状态</th>
                                    <th>车辆名称及ID</th>
                                    <th>异常原因</th>
                                    <th>车型ID</th>
                                    <th>报文ID</th>
                                    <th style="text-align: right;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <span class="status-tag status-normal">正常</span>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 12px;">
                                            <div style="width: 32px; height: 32px; background: rgba(24, 144, 255, 0.1); border-radius: 6px; display: flex; align-items: center; justify-content: center; color: #1890ff;">
                                                🚗
                                            </div>
                                            <div>
                                                <div style="font-weight: 500;">大众朗逸</div>
                                                <div style="font-size: 12px; color: #00000073;">VEH-20230615-0001</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td style="color: #00000073;">-</td>
                                    <td>TYPE-001</td>
                                    <td>
                                        <span style="color: #1890ff; cursor: pointer; font-size: 12px;" title="RES-20230615-0012-38726192837461928374">
                                            RES-20230615-0012
                                        </span>
                                    </td>
                                    <td style="text-align: right;">
                                        <button class="btn" style="background: rgba(24, 144, 255, 0.1); color: #1890ff; border-color: rgba(24, 144, 255, 0.2);" onclick="showVehicleDetail()">
                                            查看详情
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="status-tag status-error">异常</span>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 12px;">
                                            <div style="width: 32px; height: 32px; background: rgba(24, 144, 255, 0.1); border-radius: 6px; display: flex; align-items: center; justify-content: center; color: #1890ff;">
                                                🚗
                                            </div>
                                            <div>
                                                <div style="font-weight: 500;">丰田卡罗拉</div>
                                                <div style="font-size: 12px; color: #00000073;">VEH-20230615-0002</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="status-tag status-error">价格未配置</span>
                                    </td>
                                    <td>TYPE-001</td>
                                    <td>
                                        <span style="color: #1890ff; cursor: pointer; font-size: 12px;" title="RES-20230615-0028-92837461928374619283">
                                            RES-20230615-0028
                                        </span>
                                    </td>
                                    <td style="text-align: right;">
                                        <button class="btn btn-danger" onclick="handleVehicleError()">
                                            去处理
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="status-tag status-normal">正常</span>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 12px;">
                                            <div style="width: 32px; height: 32px; background: rgba(24, 144, 255, 0.1); border-radius: 6px; display: flex; align-items: center; justify-content: center; color: #1890ff;">
                                                🚗
                                            </div>
                                            <div>
                                                <div style="font-weight: 500;">本田思域</div>
                                                <div style="font-size: 12px; color: #00000073;">VEH-20230615-0003</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td style="color: #00000073;">-</td>
                                    <td>TYPE-002</td>
                                    <td>
                                        <span style="color: #1890ff; cursor: pointer; font-size: 12px;" title="RES-20230615-0045-19283746192837461928">
                                            RES-20230615-0045
                                        </span>
                                    </td>
                                    <td style="text-align: right;">
                                        <button class="btn" style="background: rgba(24, 144, 255, 0.1); color: #1890ff; border-color: rgba(24, 144, 255, 0.2);" onclick="showVehicleDetail()">
                                            查看详情
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="status-tag status-error">异常</span>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 12px;">
                                            <div style="width: 32px; height: 32px; background: rgba(24, 144, 255, 0.1); border-radius: 6px; display: flex; align-items: center; justify-content: center; color: #1890ff;">
                                                🚗
                                            </div>
                                            <div>
                                                <div style="font-weight: 500;">别克君威</div>
                                                <div style="font-size: 12px; color: #00000073;">VEH-20230615-0004</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="status-tag status-error">图片缺失</span>
                                    </td>
                                    <td>TYPE-002</td>
                                    <td>
                                        <span style="color: #1890ff; cursor: pointer; font-size: 12px;" title="RES-20230615-0056-28374619283746192837">
                                            RES-20230615-0056
                                        </span>
                                    </td>
                                    <td style="text-align: right;">
                                        <button class="btn btn-danger" onclick="handleVehicleError()">
                                            去处理
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="status-tag status-normal">正常</span>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 12px;">
                                            <div style="width: 32px; height: 32px; background: rgba(24, 144, 255, 0.1); border-radius: 6px; display: flex; align-items: center; justify-content: center; color: #1890ff;">
                                                🚗
                                            </div>
                                            <div>
                                                <div style="font-weight: 500;">大众帕萨特</div>
                                                <div style="font-size: 12px; color: #00000073;">VEH-20230615-0005</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td style="color: #00000073;">-</td>
                                    <td>TYPE-002</td>
                                    <td>
                                        <span style="color: #1890ff; cursor: pointer; font-size: 12px;" title="RES-20230615-0067-37461928374619283746">
                                            RES-20230615-0067
                                        </span>
                                    </td>
                                    <td style="text-align: right;">
                                        <button class="btn" style="background: rgba(24, 144, 255, 0.1); color: #1890ff; border-color: rgba(24, 144, 255, 0.2);" onclick="showVehicleDetail()">
                                            查看详情
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="status-tag status-error">异常</span>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 12px;">
                                            <div style="width: 32px; height: 32px; background: rgba(24, 144, 255, 0.1); border-radius: 6px; display: flex; align-items: center; justify-content: center; color: #1890ff;">
                                                🚗
                                            </div>
                                            <div>
                                                <div style="font-weight: 500;">丰田RAV4</div>
                                                <div style="font-size: 12px; color: #00000073;">VEH-20230615-0006</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="status-tag status-error">状态异常</span>
                                    </td>
                                    <td>TYPE-004</td>
                                    <td>
                                        <span style="color: #1890ff; cursor: pointer; font-size: 12px;" title="RES-20230615-0078-46192837461928374619">
                                            RES-20230615-0078
                                        </span>
                                    </td>
                                    <td style="text-align: right;">
                                        <button class="btn btn-danger" onclick="handleVehicleError()">
                                            去处理
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <!-- 分页 -->
                        <div class="pagination">
                            <div class="pagination-info">
                                显示 <strong>1</strong> 到 <strong>6</strong> 条，共 <strong style="color: #1890ff;">128</strong> 条
                            </div>
                            <div class="pagination-controls">
                                <button class="page-btn">‹</button>
                                <button class="page-btn active">1</button>
                                <button class="page-btn">2</button>
                                <button class="page-btn">3</button>
                                <button class="page-btn">4</button>
                                <button class="page-btn">5</button>
                                <span style="color: #00000073;">...</span>
                                <button class="page-btn">22</button>
                                <button class="page-btn">›</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 异常分析区域 -->
                <div class="chart-container fade-in">
                    <!-- 异常类型分布 -->
                    <div class="chart-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <h3 class="chart-title">📊 异常类型分布</h3>
                            <select style="padding: 4px 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                <option>近7天</option>
                                <option>近30天</option>
                                <option>近90天</option>
                            </select>
                        </div>
                        <div style="height: 200px; display: flex; align-items: center; justify-content: center;">
                            <canvas id="errorTypeChart" style="max-height: 200px;"></canvas>
                        </div>
                        <div class="chart-legend">
                            <div class="legend-item">
                                <div class="legend-color" style="background: #ff4d4f;"></div>
                                <span>价格异常 28%</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: #faad14;"></div>
                                <span>信息缺失 35%</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: #1890ff;"></div>
                                <span>状态异常 18%</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: #52c41a;"></div>
                                <span>其他异常 19%</span>
                            </div>
                        </div>
                    </div>

                    <!-- 异常趋势 -->
                    <div class="chart-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <h3 class="chart-title">📈 异常趋势</h3>
                            <div style="display: flex; gap: 8px;">
                                <button class="btn btn-primary" style="padding: 4px 12px; font-size: 12px;">日</button>
                                <button class="btn" style="padding: 4px 12px; font-size: 12px;">周</button>
                                <button class="btn" style="padding: 4px 12px; font-size: 12px;">月</button>
                            </div>
                        </div>
                        <div style="height: 200px;">
                            <canvas id="errorTrendChart" style="max-height: 200px;"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 最近操作记录 -->
                <div class="content-card fade-in">
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px 24px; border-bottom: 1px solid #f0f0f0;">
                        <h3 style="font-size: 16px; font-weight: 500; margin: 0;">🕒 最近操作记录</h3>
                        <button class="btn" style="color: #1890ff;">查看全部</button>
                    </div>
                    <div class="card-body" style="padding-top: 0;">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>操作时间</th>
                                    <th>操作类型</th>
                                    <th>操作内容</th>
                                    <th>操作人</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2023-06-15 14:30</td>
                                    <td>修改价格</td>
                                    <td>丰田卡罗拉(京A12345) 价格从¥198/天修改为¥268/天</td>
                                    <td>张三</td>
                                    <td><span class="status-tag status-normal">成功</span></td>
                                </tr>
                                <tr>
                                    <td>2023-06-15 11:45</td>
                                    <td>上传图片</td>
                                    <td>别克君威(京B56789) 补充上传车辆图片</td>
                                    <td>李四</td>
                                    <td><span class="status-tag status-normal">成功</span></td>
                                </tr>
                                <tr>
                                    <td>2023-06-15 09:20</td>
                                    <td>更新状态</td>
                                    <td>丰田RAV4(京C98765) 状态从"不可用"更新为"可用"</td>
                                    <td>王五</td>
                                    <td><span class="status-tag status-normal">成功</span></td>
                                </tr>
                                <tr>
                                    <td>2023-06-14 16:10</td>
                                    <td>批量导入</td>
                                    <td>批量导入10辆新车信息</td>
                                    <td>赵六</td>
                                    <td><span class="status-tag status-warning">部分成功</span></td>
                                </tr>
                                <tr>
                                    <td>2023-06-14 10:05</td>
                                    <td>系统检测</td>
                                    <td>执行每日系统检测任务</td>
                                    <td>系统</td>
                                    <td><span class="status-tag status-normal">成功</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 车辆详情模态框 -->
    <div class="modal-overlay" id="vehicleDetailModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">车辆详情</h3>
                <span class="modal-close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px; margin-bottom: 16px;">
                    <div>
                        <div style="color: #00000073; margin-bottom: 4px;">车辆名称</div>
                        <div style="font-weight: 500;">丰田卡罗拉</div>
                    </div>
                    <div>
                        <div style="color: #00000073; margin-bottom: 4px;">车辆ID</div>
                        <div style="font-weight: 500;">VEH-20230615-0002</div>
                    </div>
                    <div>
                        <div style="color: #00000073; margin-bottom: 4px;">车型</div>
                        <div style="font-weight: 500;">经济型</div>
                    </div>
                    <div>
                        <div style="color: #00000073; margin-bottom: 4px;">座位数</div>
                        <div style="font-weight: 500;">5座</div>
                    </div>
                    <div>
                        <div style="color: #00000073; margin-bottom: 4px;">车牌号</div>
                        <div style="font-weight: 500;">京A12345</div>
                    </div>
                    <div>
                        <div style="color: #00000073; margin-bottom: 4px;">车型ID</div>
                        <div style="font-weight: 500;">TYPE-001</div>
                    </div>
                    <div>
                        <div style="color: #00000073; margin-bottom: 4px;">取车门店</div>
                        <div style="font-weight: 500;">上海虹桥机场店</div>
                    </div>
                    <div>
                        <div style="color: #00000073; margin-bottom: 4px;">报文ID</div>
                        <div style="font-weight: 500;">RES-20230615-0028</div>
                    </div>
                </div>
                
                <div style="margin-bottom: 16px;">
                    <div style="color: #00000073; margin-bottom: 4px;">异常原因</div>
                    <div style="padding: 12px; background: #fff2f0; color: #ff4d4f; border-radius: 6px; border: 1px solid #ffccc7;">
                        价格未配置 - 该车辆在渠道系统中未配置价格信息，导致无法正常展示价格。
                    </div>
                </div>
                
                <div>
                    <div style="color: #00000073; margin-bottom: 4px;">详细描述</div>
                    <div style="background: #f0f2f5; padding: 12px; border-radius: 6px; color: #00000073; font-size: 14px;">
                        车辆信息在渠道系统中存在缺失，导致无法正常展示。请检查并补充以下信息：
                        <ul style="margin: 8px 0 0 20px;">
                            <li>每日基础价格</li>
                            <li>保险费用</li>
                            <li>附加服务费用</li>
                            <li>押金金额</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="closeModal()">取消</button>
                <button class="btn btn-danger">立即处理</button>
            </div>
        </div>
    </div>

    <script>
        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 异常类型分布图表
            const errorTypeCtx = document.getElementById('errorTypeChart').getContext('2d');
            new Chart(errorTypeCtx, {
                type: 'doughnut',
                data: {
                    labels: ['价格异常', '信息缺失', '状态异常', '其他异常'],
                    datasets: [{
                        data: [28, 35, 18, 19],
                        backgroundColor: ['#ff4d4f', '#faad14', '#1890ff', '#52c41a'],
                        borderWidth: 0,
                        hoverOffset: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.label}: ${context.raw}%`;
                                }
                            }
                        }
                    },
                    cutout: '60%'
                }
            });
            
            // 异常趋势图表
            const errorTrendCtx = document.getElementById('errorTrendChart').getContext('2d');
            new Chart(errorTrendCtx, {
                type: 'line',
                data: {
                    labels: ['1日', '2日', '3日', '4日', '5日', '6日', '7日'],
                    datasets: [{
                        label: '异常车辆数',
                        data: [12, 15, 18, 14, 20, 16, 22],
                        borderColor: '#1890ff',
                        backgroundColor: 'rgba(24, 144, 255, 0.1)',
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#FFFFFF',
                        pointBorderColor: '#1890ff',
                        pointBorderWidth: 2,
                        pointRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(0, 0, 0, 0.05)' }
                        },
                        x: {
                            grid: { display: false }
                        }
                    },
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#FFFFFF',
                            bodyColor: '#FFFFFF',
                            borderColor: 'rgba(255, 255, 255, 0.1)',
                            borderWidth: 1,
                            padding: 12,
                            cornerRadius: 6
                        }
                    }
                }
            });

            // 添加页面动画
            const elements = document.querySelectorAll('.fade-in');
            elements.forEach((el, index) => {
                el.style.animationDelay = `${index * 0.1}s`;
            });
        });

        // 模态框功能
        function showVehicleDetail() {
            document.getElementById('vehicleDetailModal').style.display = 'flex';
        }

        function closeModal() {
            document.getElementById('vehicleDetailModal').style.display = 'none';
        }

        function handleVehicleError() {
            alert('跳转到车辆信息编辑页面进行处理...');
        }

        // 标签页切换
        document.querySelectorAll('.tab-item').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.tab-item').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 点击模态框外部关闭
        document.getElementById('vehicleDetailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html> 