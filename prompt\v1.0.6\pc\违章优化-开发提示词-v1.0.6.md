# 违章优化功能开发提示词

## 🎯 开发目标

在 PC 端 SaaS 系统的车辆/违章管理页面中新增转移失败提示功能，当转移状态为失败时，在转移状态列中显示提示图标，鼠标悬停时展示失败原因。

## 🧱 技术栈要求

- 框架：React@18.x
- 组件库：Ant Design@5.12.8
- 样式库：Less
- 类型安全：TypeScript @4.x
- 状态管理：React Hooks (useState, useEffect)
- 接口请求：Axios
- 单元测试：Jest
- 文档注释：jsdoc 规范

## 📋 代码规范检查

- [ ] 命名规范：组件名、方法名、变量名符合项目规范
- [ ] 异常处理：使用统一的 try catch 和错误码
- [ ] 日志记录：关键操作添加适当的日志
- [ ] 参数验证：外部输入进行必要的验证
- [ ] 接口响应：处理返回格式符合项目规范
- [ ] 类型定义：完整的 TypeScript 类型定义

## 🧩 页面/组件功能描述

### 1. 功能清单

- [ ] 转移状态列显示：根据 transferFailReason 字段判断是否显示提示图标
- [ ] 悬停提示功能：鼠标悬停时显示 Popover 提示，内容为 transferFailReason 字段值
- [ ] 条件渲染：该字段有值则展示提示图标，无值则不展示
- [ ] 样式一致性：保持与现有设计风格完全一致

### 2. 页面结构

- 顶部搜索区：门店、关联订单号、车牌号、创建日期、操作时间、违章时间、交管违章处理状态、转移状态筛选
- 中部表格区：包含转移状态列，新增转移失败提示功能
- 底部：分页组件

## 🧮 接口与数据结构

### 1. 接口列表

- GET `/api/violation/list`：获取违章列表数据，参数为 `page`, `size`, `keyword`, `storeId`, `orderNo`, `plateNo`, `createTimeStart`, `createTimeEnd`, `operateTimeStart`, `operateTimeEnd`, `violationTimeStart`, `violationTimeEnd`, `trafficStatus`, `transferStatus`

### 2. 数据字段说明

| 字段               | 类型   | 描述             | 是否必填 |
| ------------------ | ------ | ---------------- | -------- |
| id                 | number | 违章记录 ID      | 是       |
| storeName          | string | 门店名称         | 是       |
| plateNo            | string | 车牌号           | 是       |
| carModel           | string | 车型信息         | 是       |
| violationCity      | string | 违章城市         | 是       |
| violationTime      | string | 违章时间         | 是       |
| penaltyPoints      | string | 罚分/罚款        | 是       |
| totalAmount        | string | 合计金额         | 是       |
| trafficStatus      | string | 交管违章处理状态 | 是       |
| documentStatus     | string | 单据处理状态     | 是       |
| transferStatus     | string | 转移状态         | 是       |
| transferFailReason | string | 转移失败原因     | 否       |
| orderNo            | string | 订单号           | 是       |
| createTime         | string | 创建时间         | 是       |
| operateTime        | string | 最新操作时间     | 是       |

## 🧩 特殊交互/边界逻辑

- 转移失败提示图标只在 transferFailReason 字段有值时显示
- 鼠标悬停时显示 Popover 提示，内容为 transferFailReason 字段值
- 提示图标样式：⚠️ 图标，颜色为 #ff4d4f，字体大小为 12px
- Popover 样式：背景色 #fff，边框色 #d9d9d9，圆角 6px，阴影 0 2px 8px rgba(0, 0, 0, 0.15)
- 表格行高：48px，单元格内边距：16px
- 字体：-apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif
- 主文本色：rgba(0, 0, 0, 0.85)，辅助文本色：rgba(0, 0, 0, 0.45)

## 📌 输出要求

- 使用函数式组件结构
- 代码需可直接运行（无需额外配置）
- 使用 React Hooks 管理状态和副作用
- 类型语言：TypeScript
- 遵守代码简约之道
- 组件需要支持国际化
- 添加完整的单元测试

## 🔄 影响范围

### 向前兼容性

- [ ] 不影响现有页面、组件功能
- [ ] 不影响现有数据结构
- [ ] 不影响现有接口调用

### 依赖关系

- **上游依赖**: 违章管理页面组件、表格组件、Popover 组件
- **下游影响**: 违章列表展示、转移状态显示

## 📝 实现细节

### 1. 组件结构

```typescript
// 转移状态列组件
interface TransferStatusProps {
  status: string;
  failReason?: string;
}

const TransferStatus: React.FC<TransferStatusProps> = ({
  status,
  failReason,
}) => {
  const [popoverVisible, setPopoverVisible] = useState(false);

  return (
    <div className="transfer-status-cell">
      <span className={`status-tag status-${getStatusType(status)}`}>
        {status}
      </span>
      {failReason && (
        <span
          className="transfer-fail-icon"
          onMouseEnter={() => setPopoverVisible(true)}
          onMouseLeave={() => setPopoverVisible(false)}
        >
          ⚠️
        </span>
      )}
      {failReason && (
        <Popover
          visible={popoverVisible}
          content={failReason}
          placement="top"
          overlayClassName="transfer-fail-popover"
        />
      )}
    </div>
  );
};
```

### 2. 样式定义

```less
.transfer-status-cell {
  display: flex;
  align-items: center;
  gap: 4px;

  .transfer-fail-icon {
    color: #ff4d4f;
    font-size: 12px;
    cursor: pointer;
    transition: opacity 0.3s;

    &:hover {
      opacity: 0.8;
    }
  }
}

.transfer-fail-popover {
  .ant-popover-inner {
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .ant-popover-arrow {
    border-color: #d9d9d9;
  }
}
```

### 3. 类型定义

```typescript
interface ViolationRecord {
  id: number;
  storeName: string;
  plateNo: string;
  carModel: string;
  violationCity: string;
  violationTime: string;
  penaltyPoints: string;
  totalAmount: string;
  trafficStatus: string;
  documentStatus: string;
  transferStatus: string;
  transferFailReason?: string;
  orderNo: string;
  createTime: string;
  operateTime: string;
}

interface ViolationListResponse {
  code: number;
  message: string;
  data: {
    list: ViolationRecord[];
    total: number;
    page: number;
    size: number;
  };
}
```

### 4. 测试用例

```typescript
describe("TransferStatus Component", () => {
  it("should render status without fail icon when no fail reason", () => {
    render(<TransferStatus status="转移成功" />);
    expect(screen.queryByText("⚠️")).not.toBeInTheDocument();
  });

  it("should render status with fail icon when has fail reason", () => {
    render(<TransferStatus status="上报失败" failReason="网络连接超时" />);
    expect(screen.getByText("⚠️")).toBeInTheDocument();
  });

  it("should show popover on hover", () => {
    render(<TransferStatus status="上报失败" failReason="网络连接超时" />);
    fireEvent.mouseEnter(screen.getByText("⚠️"));
    expect(screen.getByText("网络连接超时")).toBeInTheDocument();
  });
});
```

## 🎨 设计规范

### 颜色规范

- 主文本色：rgba(0, 0, 0, 0.85)
- 辅助文本色：rgba(0, 0, 0, 0.45)
- 失败状态色：#ff4d4f
- 警告状态色：#fa8c16
- 成功状态色：#52c41a
- 默认状态色：#d9d9d9

### 字体规范

- 字体族：-apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif
- 主文本大小：12px
- 行高：18.858px
- 标题大小：20px
- 辅助文本大小：11px

### 间距规范

- 表格行高：48px
- 单元格内边距：16px
- 组件间距：8px
- 图标间距：4px

## 📋 开发检查清单

- [ ] 转移状态列正确显示
- [ ] 转移失败图标条件渲染
- [ ] Popover 提示功能正常
- [ ] 样式与设计稿一致
- [ ] TypeScript 类型定义完整
- [ ] 单元测试覆盖完整
- [ ] 代码注释规范
- [ ] 性能优化（避免不必要的重渲染）
- [ ] 错误处理完善
- [ ] 国际化支持

## 🔧 部署说明

1. 确保 Ant Design 版本为 5.12.8
2. 确保 Less 样式编译配置正确
3. 确保 TypeScript 配置支持 React 18
4. 确保测试环境配置完整
5. 确保构建脚本正确配置

**注意：本开发提示词基于擎路 AI 项目的实际架构和规范制定，请根据项目发展持续更新和完善。**
