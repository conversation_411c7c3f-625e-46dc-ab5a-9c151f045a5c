# 车辆管理页面开发提示词

## 项目背景

开发一个汽车租赁管理系统的车型管理页面，该页面是车辆资产管理的核心模块，需要支持车型的增删改查、多平台集成、权限控制等功能。

## 技术栈要求

- React 18+ with TypeScript
- Ant Design 5.x 组件库
- React Router 6.x 路由管理
- CSS Modules 样式管理
- 自定义 Hook 和组件封装

## 核心功能实现

### 1. 数据模型设计

```typescript
interface VehicleModel {
  id: number;
  vehicleModelImgUrl: string | null;
  vehicleBrandName: string | null;
  vehicleSeryName: string | null;
  vehicleSubSeryName: string | null;
  vehicleYearStyle: string;
  vehicleModelGroupName: string | null;
  licenseType: string;
  carriage: string;
  displacement: string;
  gearbox: string;
  doors: string;
  seatNum: number;
  fuelForm: string;
  driveType: 0 | 2 | 3; // 2四驱 3两驱
  hasSunroof: 0 | 1;
  hasSnowTires: 0 | 1;
  selfServiceReturn: 0 | 1;
  fastChargeTime: 1 | 0;
  tagList: { id: number; tagName: string }[] | null;
  channelBindList: VehicleModelChannelBind[] | null;
}
```

### 2. 页面布局结构

- 顶部搜索区域：多条件筛选表单
- 中间操作栏：统计信息、排序、功能按钮
- 底部表格区域：车型列表展示
- 弹窗组件：车型标签管理

### 3. 搜索功能实现

```typescript
// 搜索条件类型
interface ModelsQuery {
  vehicleBrandIdList?: number[];
  vehicleModelGroup?: string;
  licenseType?: string;
  channelId?: number;
  bindChannelVehicleId?: string;
  ctripSynced?: number;
  pageIndex?: number;
  pageSize?: number;
}

// 搜索组件集成
<Search<ModelsQuery>
  initialValues={{} as ModelsQuery}
  onSearch={(values) => {
    setQuery({ ...query, ...values, pageIndex: 1 });
  }}
>
  <Form.Item label="车型" name="idList">
    <Vehicles multiple />
  </Form.Item>
  <Form.Item label="品牌" name="vehicleBrandIdList">
    <ChooseBrands multiple />
  </Form.Item>
  // ... 其他搜索条件
</Search>;
```

### 4. 动态表格列实现

```typescript
// 可展开的车辆信息列
const columns: ColumnsType<VehicleModel> = [
  {
    title: columnsTitle, // 自定义标题组件
    children: isTitle
      ? [
          { title: "车牌类型", dataIndex: "licenseType", width: 100 },
          { title: "厢式", dataIndex: "carriage", width: 100 },
          { title: "排量", dataIndex: "displacement", width: 100 },
          // ... 更多详细列
        ]
      : [{ title: "车牌类型", dataIndex: "licenseType", width: 100 }],
  },
];

// 自定义渲染函数
const renderActions = (model: VehicleModel) => (
  <>
    <Button type="link" onClick={() => navigate(`/models/${model.id}`)}>
      详情
    </Button>
    <Roles allowed="car:model:edit">
      <Button type="link" onClick={() => navigate(`/models/edit/${model.id}`)}>
        修改
      </Button>
    </Roles>
    <Roles allowed="car:model:remove">
      <Popconfirm title="确定删除此车型吗？" onConfirm={() => remove(model.id)}>
        <Button danger type="link">
          删除
        </Button>
      </Popconfirm>
    </Roles>
  </>
);
```

### 5. 网络平台集成

```typescript
// 渠道绑定状态渲染
const renderChannelId = (channelId: number, model: VehicleModel) => {
  const channel = model.channelBindList?.find((v) => v.channelId === channelId);

  // 特殊渠道处理（如飞猪）
  if (channelId === 3 && channel?.bindChannelVehicleName) {
    return (
      <div styleName="channel-cell">
        <div styleName="name">
          <div>旧：{channel.bindChannelVehicleName}</div>
          {channel.newBindChannelVehicleName && (
            <div>新：{channel.newBindChannelVehicleName}</div>
          )}
        </div>
      </div>
    );
  }

  // 携程同步状态显示
  const status =
    channel && channelId === 2
      ? channel.synced === 1
        ? true
        : channel.syncFailedReason
      : null;

  return (
    <div styleName="channel-cell">
      {status !== null && (
        <Tooltip title={status === true ? "创建成功" : status}>
          <div
            styleName={c("status", status === true ? "synced" : "not-synced")}
          />
        </Tooltip>
      )}
      <div styleName="name">{name}</div>
    </div>
  );
};
```

### 6. 权限控制实现

```typescript
// 基于角色的组件包装
<Roles allowed="car:model:export">
  <Export filename="导出车型.xlsx" execute={async () => exportModels(query)} />
</Roles>

<Roles allowed="car:model:add">
  <Button type="primary" onClick={() => navigate('/models/add/')}>
    新增车型
  </Button>
</Roles>
```

### 7. 状态管理

```typescript
// 主要状态
const [data, setData] = useState<{
  count: number;
  list: VehicleModel[];
  final: boolean;
}>({
  count: 0,
  list: [],
  final: false,
});
const [query, setQuery] = useState<
  RequiredFields<ModelsQuery, "pageIndex" | "pageSize">
>({
  pageIndex: 1,
  pageSize: 10,
});
const [isTitle, setIsTitle] = useState<boolean>(false); // 控制列展开状态
```

### 8. 数据获取与更新

```typescript
// 数据获取Hook
const doQuery = useCallback((q: typeof query) => {
  void getModels(q).then((res) => {
    if (res.success) {
      const data = res.data;
      const final = data.list.length < q.pageSize;
      setData({ ...data, final });
    }
  });
}, []);

// 删除操作
const remove = async (id: number) => {
  const res = await removeModel(id);
  if (res.success) {
    message.success("车型已删除");
    void doQuery(query);
  } else {
    message.error("车型删除失败：" + res.error.message);
  }
};
```

### 9. 样式设计要点

- 使用 CSS Modules 避免样式冲突
- 响应式表格设计，支持横向滚动
- 车型图片展示优化
- 状态指示器样式（同步状态、展开状态等）
- 操作按钮布局优化

### 10. 性能优化

- 使用 useCallback 优化函数引用
- 分页加载减少初始渲染压力
- 条件渲染减少不必要的组件渲染
- 表格虚拟化处理大量数据

### 11. 错误处理

- API 调用错误提示
- 网络异常处理
- 权限不足提示
- 数据验证错误处理

### 12. 测试要点

- 搜索功能测试
- 权限控制测试
- 表格交互测试
- 数据导出测试
- 响应式布局测试

## 开发注意事项

1. 确保 TypeScript 类型定义完整
2. 遵循项目的代码规范和组件封装模式
3. 注意权限控制的完整性
4. 优化用户体验，提供清晰的反馈信息
5. 考虑数据安全和隐私保护
6. 保持代码可维护性和可扩展性
