<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>擎路SaaS - PC订单列表复制功能原型图 v1.0.1</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
        }

        /* 顶部导航栏 */
        .top-navbar {
            background: #ffffff;
            height: 56px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            border-bottom: 1px solid #e8e8e8;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #1890ff, #722ed1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }

        .company-name {
            font-size: 16px;
            font-weight: 600;
            color: #1890ff;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 16px;
            color: #666;
        }

        /* 主要布局 */
        .main-layout {
            display: flex;
            height: calc(100vh - 56px);
        }

        /* 左侧导航 */
        .sidebar {
            width: 200px;
            background: #001529;
            color: white;
            overflow-y: auto;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            cursor: pointer;
            transition: background-color 0.2s;
            gap: 8px;
        }

        .nav-item:hover {
            background: #1890ff;
        }

        .nav-item.active {
            background: #1890ff;
        }

        .nav-item.has-children {
            background: #1c2b36;
        }

        .nav-icon {
            width: 16px;
            height: 16px;
            opacity: 0.8;
        }

        /* 内容区域 */
        .content-area {
            flex: 1;
            background: #f5f5f5;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* 标签页导航 */
        .tabs-container {
            background: white;
            border-bottom: 1px solid #e8e8e8;
            padding: 0 24px;
            display: flex;
            align-items: center;
            height: 48px;
        }

        .tab-item {
            padding: 8px 16px;
            cursor: pointer;
            border-radius: 4px;
            margin-right: 8px;
            background: #f0f0f0;
            color: #666;
            position: relative;
        }

        .tab-item.active {
            background: #1890ff;
            color: white;
        }

        .tab-close {
            margin-left: 8px;
            cursor: pointer;
            opacity: 0.6;
        }

        /* 页面内容 */
        .page-content {
            flex: 1;
            padding: 24px;
            overflow: auto;
        }

        /* 搜索表单 */
        .search-form {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            margin-bottom: 16px;
        }

        .form-row {
            display: flex;
            gap: 24px;
            margin-bottom: 16px;
            align-items: center;
        }

        .form-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-label {
            color: #333;
            white-space: nowrap;
            font-weight: 500;
        }

        .form-input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            outline: none;
            transition: border-color 0.2s;
            min-width: 120px;
        }

        .form-input:focus {
            border-color: #1890ff;
            box-shadow: 0 0 4px rgba(24, 144, 255, 0.2);
        }

        .form-select {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            outline: none;
            min-width: 120px;
            background: white;
        }

        .status-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .status-tag {
            padding: 4px 8px;
            background: #f0f0f0;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            color: #666;
        }

        .status-tag.selected {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .form-buttons {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .btn-primary {
            background: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background: #40a9ff;
        }

        .btn-default {
            background: white;
            color: #333;
            border: 1px solid #d9d9d9;
        }

        .btn-default:hover {
            color: #1890ff;
            border-color: #1890ff;
        }

        /* 订单状态统计 */
        .order-stats {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            margin-bottom: 16px;
        }

        .stats-tabs {
            display: flex;
            border-bottom: 1px solid #e8e8e8;
            padding: 0 24px;
        }

        .stats-tab {
            padding: 16px 24px;
            cursor: pointer;
            position: relative;
            color: #666;
            transition: color 0.2s;
        }

        .stats-tab.active {
            color: #1890ff;
        }

        .stats-tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            height: 2px;
            background: #1890ff;
        }

        .stats-count {
            margin-left: 4px;
            color: #999;
        }

        /* 操作栏 */
        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px;
            background: white;
        }

        .action-left {
            display: flex;
            gap: 12px;
        }

        .action-right {
            display: flex;
            gap: 12px;
        }

        /* 订单表格 */
        .order-table {
            background: white;
            border-radius: 0 0 8px 8px;
            overflow: hidden;
        }

        .table-header {
            background: #fafafa;
            display: grid;
            grid-template-columns: 180px 100px 280px 150px 300px 120px 100px;
            padding: 16px 24px;
            font-weight: 600;
            color: #333;
            border-bottom: 1px solid #e8e8e8;
        }

        .table-row {
            display: grid;
            grid-template-columns: 180px 100px 280px 150px 300px 120px 100px;
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s;
        }

        .table-row:hover {
            background: #fafafa;
        }

        .cell-content {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .order-info {
            color: #666;
            font-size: 12px;
        }

        .order-number {
            color: #1890ff;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .vehicle-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .vehicle-plate {
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .vehicle-model {
            color: #666;
            font-size: 12px;
        }

        .vehicle-brand {
            color: #999;
            font-size: 11px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .status-completed {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .time-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .time-item {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .time-label {
            font-size: 12px;
            color: #666;
        }

        .time-value {
            font-size: 12px;
            color: #333;
        }

        .time-duration {
            color: #ff4d4f;
            font-weight: 500;
        }

        .price-info {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        /* 新增：复制图标样式 */
        .copy-icon {
            width: 16px;
            height: 16px;
            cursor: pointer;
            opacity: 0.6;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 2px;
            padding: 2px;
        }

        .copy-icon:hover {
            opacity: 1;
            background: #f0f0f0;
        }

        .copy-icon svg {
            width: 12px;
            height: 12px;
            fill: #666;
        }

        .copy-icon:hover svg {
            fill: #1890ff;
        }

        /* 复制成功提示 */
        .copy-toast {
            position: fixed;
            top: 80px;
            right: 24px;
            background: #52c41a;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .copy-toast.show {
            transform: translateX(0);
        }

        /* 渠道订单号显示（新增列） */
        .table-header-new {
            display: grid;
            grid-template-columns: 180px 100px 180px 180px 150px 240px 120px 100px;
            padding: 16px 24px;
            font-weight: 600;
            color: #333;
            border-bottom: 1px solid #e8e8e8;
        }

        .table-row-new {
            display: grid;
            grid-template-columns: 180px 100px 180px 180px 150px 240px 120px 100px;
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s;
        }

        .channel-order {
            color: #1890ff;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 变更标注样式 */
        .change-highlight {
            position: relative;
        }

        .change-highlight::before {
            content: '新增';
            position: absolute;
            top: -8px;
            right: 0;
            background: #ff4d4f;
            color: white;
            font-size: 10px;
            padding: 2px 4px;
            border-radius: 2px;
            font-weight: normal;
        }

        .enhancement-highlight {
            position: relative;
        }

        .enhancement-highlight::before {
            content: '增强';
            position: absolute;
            top: -8px;
            right: 0;
            background: #1890ff;
            color: white;
            font-size: 10px;
            padding: 2px 4px;
            border-radius: 2px;
            font-weight: normal;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="top-navbar">
        <div class="logo-section">
            <div class="logo">擎</div>
            <div class="company-name">擎路科技</div>
        </div>
        <div class="user-section">
            <span>企鹅123 退出</span>
        </div>
    </div>

    <!-- 主要布局 -->
    <div class="main-layout">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <div class="nav-item">
                <span class="nav-icon">🏠</span>
                <span>前台</span>
            </div>
            <div class="nav-item active has-children">
                <span class="nav-icon">📋</span>
                <span>订单</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">📦</span>
                <span>库存</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">💰</span>
                <span>价格</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">📊</span>
                <span>数据中心</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">🏪</span>
                <span>门店</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">🚗</span>
                <span>车辆</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">⚙️</span>
                <span>ETC管理</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">🏢</span>
                <span>商家</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">📋</span>
                <span>任务</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">💳</span>
                <span>账单</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">📢</span>
                <span>营销</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">🔗</span>
                <span>渠道</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">👥</span>
                <span>用户</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">🛒</span>
                <span>采购商城</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">💰</span>
                <span>财务管理</span>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 标签页 -->
            <div class="tabs-container">
                <div class="tab-item">
                    <span>前台</span>
                    <span class="tab-close">×</span>
                </div>
                <div class="tab-item active">
                    <span>短租订单</span>
                    <span class="tab-close">×</span>
                </div>
                <div class="tab-item">
                    <span>前台</span>
                    <span class="tab-close">×</span>
                </div>
            </div>

            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 搜索表单 -->
                <div class="search-form">
                    <div class="form-row">
                        <div class="form-item">
                            <span class="form-label">订单信息：</span>
                            <input type="text" class="form-input" placeholder="手机号" value="">
                        </div>
                        <div class="form-item">
                            <span class="form-label">订单来源：</span>
                            <select class="form-select">
                                <option>全部</option>
                            </select>
                        </div>
                        <div class="form-item">
                            <span class="form-label">车型：</span>
                            <input type="text" class="form-input" placeholder="ID/名称" value="">
                        </div>
                        <div class="form-buttons">
                            <button class="btn btn-primary">查询</button>
                            <button class="btn btn-default">重置</button>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <span class="form-label">订单状态：</span>
                            <div class="status-tags">
                                <span class="status-tag selected">已确认</span>
                                <span class="status-tag selected">已排车</span>
                                <span class="status-tag selected">已取车</span>
                                <span class="status-tag selected">已还车</span>
                            </div>
                        </div>
                        <div class="form-item">
                            <span class="form-label">订单标签：</span>
                            <select class="form-select">
                                <option>全部</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <span class="form-label">高级筛选 ∨</span>
                    </div>
                </div>

                <!-- 订单状态统计 -->
                <div class="order-stats">
                    <div class="stats-tabs">
                        <div class="stats-tab active">
                            全部<span class="stats-count">(186)</span>
                        </div>
                        <div class="stats-tab">
                            今日待取车<span class="stats-count">(0)</span>
                        </div>
                        <div class="stats-tab">
                            今日待还车<span class="stats-count">(0)</span>
                        </div>
                        <div class="stats-tab">
                            今日待排车<span class="stats-count">(0)</span>
                        </div>
                        <div class="stats-tab">
                            近2日待取车<span class="stats-count">(0)</span>
                        </div>
                        <div class="stats-tab">
                            近2日待还车<span class="stats-count">(0)</span>
                        </div>
                        <div class="stats-tab">
                            逾期未取车<span class="stats-count">(132)</span>
                        </div>
                        <div class="stats-tab">
                            逾期未还车<span class="stats-count">(10)</span>
                        </div>
                        <div class="stats-tab">
                            待费</div>
                        <div class="stats-tab">...</div>
                    </div>

                    <!-- 操作栏 -->
                    <div class="action-bar">
                        <div class="action-left"></div>
                        <div class="action-right">
                            <button class="btn btn-primary">新增订单</button>
                            <button class="btn btn-default">导出订单</button>
                            <button class="btn btn-default">新增标签</button>
                        </div>
                    </div>

                    <!-- 订单表格 -->
                    <div class="order-table">
                        <div class="table-header-new change-highlight">
                            <div>创建时间</div>
                            <div>状态</div>
                            <div class="enhancement-highlight">预订车辆</div>
                            <div class="change-highlight">渠道订单号</div>
                            <div>自定义标签</div>
                            <div>取还</div>
                            <div>承租人</div>
                            <div>订单总价</div>
                        </div>

                        <!-- 订单行1 -->
                        <div class="table-row-new">
                            <div class="cell-content">
                                <div>2025-07-03 14:18</div>
                                <div class="order-info">来源：线下订单</div>
                                <div class="order-info">渠道订单号：-</div>
                                <div class="order-number enhancement-highlight">
                                    订单号1705746
                                    <div class="copy-icon" onclick="copyToClipboard('1705746')" title="复制订单号">
                                        <svg viewBox="0 0 1024 1024">
                                            <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.4c2 0.3 4.1 0.5 6.1 0.5H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM672 888H382V746c0-22.1-17.9-40-40-40H200V256h472v632z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <span class="status-badge status-completed">已排车</span>
                                <div class="order-info" style="margin-top: 8px;">
                                    取车：待排司机<br>
                                    还车：待排司机
                                </div>
                            </div>
                            <div class="vehicle-info enhancement-highlight">
                                <div class="vehicle-plate">
                                    珠BF06517
                                    <div class="copy-icon" onclick="copyToClipboard('珠BF06517')" title="复制车牌号">
                                        <svg viewBox="0 0 1024 1024">
                                            <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.4c2 0.3 4.1 0.5 6.1 0.5H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM672 888H382V746c0-22.1-17.9-40-40-40H200V256h472v632z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="vehicle-model">1376-大众 迈腾GTE插电混动 2020款 GTE 豪华型</div>
                                <div class="vehicle-brand">华型 有天窗 智牌</div>
                                <div style="margin-top: 4px;">
                                    <span class="status-badge" style="background: #fff7e6; color: #fa8c16; border: 1px solid #ffd591;">押金未收</span>
                                </div>
                            </div>
                            <div class="change-highlight">
                                <div class="channel-order">
                                    CH2025070314
                                    <div class="copy-icon" onclick="copyToClipboard('CH2025070314')" title="复制渠道订单号">
                                        <svg viewBox="0 0 1024 1024">
                                            <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.4c2 0.3 4.1 0.5 6.1 0.5H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM672 888H382V746c0-22.1-17.9-40-40-40H200V256h472v632z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div></div>
                            <div class="time-info">
                                <div class="time-item">
                                    <div class="time-label">预计 2025-07-03 14:18 至 2025-07-04 14:18 <span class="time-duration">1天</span></div>
                                    <div class="time-value">取车：神风租车22(固店取车)</div>
                                    <div class="time-value">云南省洱海子湖公司公园分店</div>
                                    <div class="time-value">还车：神风租车22(固店还车)</div>
                                    <div class="time-value">云南省洱海子湖公司公园分店</div>
                                </div>
                            </div>
                            <div>管路客服小丽</div>
                            <div class="price-info">¥ 311</div>
                        </div>

                        <!-- 订单行2 -->
                        <div class="table-row-new">
                            <div class="cell-content">
                                <div>2025-07-03 13:57</div>
                                <div class="order-info">来源：线下订单</div>
                                <div class="order-info">渠道订单号：-</div>
                                <div class="order-number enhancement-highlight">
                                    订单号1705745
                                    <div class="copy-icon" onclick="copyToClipboard('1705745')" title="复制订单号">
                                        <svg viewBox="0 0 1024 1024">
                                            <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.4c2 0.3 4.1 0.5 6.1 0.5H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM672 888H382V746c0-22.1-17.9-40-40-40H200V256h472v632z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <span class="status-badge status-completed">已排车</span>
                                <div class="order-info" style="margin-top: 8px;">
                                    取车：待排司机<br>
                                    还车：待排司机
                                </div>
                            </div>
                            <div class="vehicle-info enhancement-highlight">
                                <div class="vehicle-plate">
                                    珠BF09400
                                    <div class="copy-icon" onclick="copyToClipboard('珠BF09400')" title="复制车牌号">
                                        <svg viewBox="0 0 1024 1024">
                                            <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.4c2 0.3 4.1 0.5 6.1 0.5H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM672 888H382V746c0-22.1-17.9-40-40-40H200V256h472v632z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="vehicle-model">1376-大众 迈腾GTE插电混动 2020款 GTE 豪华型</div>
                                <div class="vehicle-brand">华型 有天窗 智牌</div>
                                <div style="margin-top: 4px;">
                                    <span class="status-badge" style="background: #fff7e6; color: #fa8c16; border: 1px solid #ffd591;">押金未收</span>
                                </div>
                            </div>
                            <div class="change-highlight">
                                <div class="channel-order">
                                    CH2025070313
                                    <div class="copy-icon" onclick="copyToClipboard('CH2025070313')" title="复制渠道订单号">
                                        <svg viewBox="0 0 1024 1024">
                                            <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.4c2 0.3 4.1 0.5 6.1 0.5H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM672 888H382V746c0-22.1-17.9-40-40-40H200V256h472v632z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div></div>
                            <div class="time-info">
                                <div class="time-item">
                                    <div class="time-label">预计 2025-07-03 13:57 至 2025-07-04 13:57 <span class="time-duration">1天</span></div>
                                    <div class="time-value">取车：神风租车22(固店取车)</div>
                                    <div class="time-value">云南省洱海子湖公司公园分店</div>
                                    <div class="time-value">还车：神风租车22(固店还车)</div>
                                    <div class="time-value">云南省洱海子湖公司公园分店</div>
                                </div>
                            </div>
                            <div>test</div>
                            <div class="price-info">¥ 311</div>
                        </div>

                        <!-- 订单行3 -->
                        <div class="table-row-new">
                            <div class="cell-content">
                                <div>2025-05-07 19:59</div>
                                <div class="order-info">来源：携程订单</div>
                                <div class="order-info">渠道订单号：-</div>
                                <div class="order-number enhancement-highlight">
                                    订单号1681694546302
                                    <div class="copy-icon" onclick="copyToClipboard('1681694546302')" title="复制订单号">
                                        <svg viewBox="0 0 1024 1024">
                                            <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.4c2 0.3 4.1 0.5 6.1 0.5H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM672 888H382V746c0-22.1-17.9-40-40-40H200V256h472v632z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <span class="status-badge status-completed">已排车</span>
                                <div class="order-info" style="margin-top: 8px;">
                                    取车：待排司机<br>
                                    还车：待排司机
                                </div>
                            </div>
                            <div class="vehicle-info enhancement-highlight">
                                <div class="vehicle-plate">
                                    珠BD04610
                                    <div class="copy-icon" onclick="copyToClipboard('珠BD04610')" title="复制车牌号">
                                        <svg viewBox="0 0 1024 1024">
                                            <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.4c2 0.3 4.1 0.5 6.1 0.5H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM672 888H382V746c0-22.1-17.9-40-40-40H200V256h472v632z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="vehicle-model">1380-别克 微蓝6 2020款 互联时尚型 PLUS 智牌</div>
                                <div style="margin-top: 4px;">
                                    <span class="status-badge" style="background: #fff7e6; color: #fa8c16; border: 1px solid #ffd591;">押金未收</span>
                                    <span class="status-badge" style="background: #e6f7ff; color: #1890ff; border: 1px solid #91d5ff;">无优惠</span>
                                </div>
                            </div>
                            <div class="change-highlight">
                                <div class="channel-order">
                                    XC19057852396
                                    <div class="copy-icon" onclick="copyToClipboard('XC19057852396')" title="复制渠道订单号">
                                        <svg viewBox="0 0 1024 1024">
                                            <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.4c2 0.3 4.1 0.5 6.1 0.5H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM672 888H382V746c0-22.1-17.9-40-40-40H200V256h472v632z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div></div>
                            <div class="time-info">
                                <div class="time-item">
                                    <div class="time-label">预计 2025-07-01 10:00 至 2025-07-04 10:30 <span class="time-duration">3天 1小时</span></div>
                                    <div class="time-value">取车：神风租车22(固店取车)</div>
                                    <div class="time-value">云南省洱海子湖公司公园分店</div>
                                    <div class="time-value">还车：神风租车22(固店还车)</div>
                                    <div class="time-value">云南省洱海子湖公司公园分店</div>
                                </div>
                            </div>
                            <div>徐铭</div>
                            <div class="price-info">¥ 659 176219057 5</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 复制成功提示 -->
    <div id="copyToast" class="copy-toast">
        复制成功！
    </div>

    <script>
        // 复制功能实现
        function copyToClipboard(text) {
            // 使用 Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(function() {
                    showCopyToast();
                }).catch(function(err) {
                    console.error('复制失败: ', err);
                    fallbackCopyTextToClipboard(text);
                });
            } else {
                // 备用方案：使用 execCommand
                fallbackCopyTextToClipboard(text);
            }
        }

        // 备用复制方案
        function fallbackCopyTextToClipboard(text) {
            var textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                var successful = document.execCommand('copy');
                if (successful) {
                    showCopyToast();
                } else {
                    console.error('复制失败');
                }
            } catch (err) {
                console.error('复制失败: ', err);
            }

            document.body.removeChild(textArea);
        }

        // 显示复制成功提示
        function showCopyToast() {
            var toast = document.getElementById('copyToast');
            toast.classList.add('show');
            
            setTimeout(function() {
                toast.classList.remove('show');
            }, 2000);
        }

        // 为演示增加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 状态标签点击效果
            var statusTags = document.querySelectorAll('.status-tag');
            statusTags.forEach(function(tag) {
                tag.addEventListener('click', function() {
                    this.classList.toggle('selected');
                });
            });

            // 统计标签点击效果
            var statsTabs = document.querySelectorAll('.stats-tab');
            statsTabs.forEach(function(tab) {
                tab.addEventListener('click', function() {
                    statsTabs.forEach(function(t) {
                        t.classList.remove('active');
                    });
                    this.classList.add('active');
                });
            });

            // 表格行悬停效果已在CSS中定义
        });
    </script>

    <!-- 页面底部说明 -->
    <div style="position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%); background: rgba(0,0,0,0.8); color: white; padding: 12px 24px; border-radius: 8px; font-size: 12px; z-index: 1000;">
        <strong>📋 擎路SaaS PC订单列表复制功能原型图 v1.0.1</strong><br>
        <span style="color: #ffd666;">🆕 新增功能：</span>订单号、渠道订单号、车牌号旁的复制图标，点击即可复制到剪贴板<br>
        <span style="color: #87d068;">✨ 增强功能：</span>原有字段保持像素级精确复刻，新增"渠道订单号"列显示渠道来源信息<br>
        <span style="color: #69c0ff;">💡 使用说明：</span>点击复制图标可快速复制对应的订单号、渠道订单号或车牌号信息
    </div>
</body>
</html> 