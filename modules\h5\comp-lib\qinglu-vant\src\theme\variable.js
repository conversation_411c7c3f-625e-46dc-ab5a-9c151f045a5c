import { useThemeVars } from '../use'

export default useThemeVars({
  buttonDefaultHeight: '40px',
  buttonBorderRadius: 'var(--van-border-radius-md)',
  buttonDefaultBackgroundColor: 'var(--face-blue-light)',
  buttonDefaultColor: 'var(--van-blue)',
  cellValueColor: 'var(--van-text-color)',
  dropdownMenuTitleActiveTextColor: 'var(--van-blue)',
  dropdownMenuOptionActiveColor: 'var(--van-blue)',
  tabActiveTextColor: 'var(--van-blue)',
  tabsBottomBarColor: 'var(--van-blue)',
})
