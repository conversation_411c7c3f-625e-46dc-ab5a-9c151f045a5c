```javascript
/*
 * @Author: duhu<PERSON>
 * @Date: 2022-10-23 19:57:38
 * @LastEditors: duhuo
 * @LastEditTime: 2022-11-12 11:15:24
 * @Description: Do not edit
 */
import <PERSON><PERSON><PERSON><PERSON> from "@lih<PERSON>sky/type-checker";
import { createRouter, createWebHashHistory } from "vue-router";

const routes = [
  {
    path: "/",
    name: "Index",
    component: () => import("./views/index/index.vue"),
    meta: {
      title: "擎路-首页",
      pageName: "首页",
    },
  },

  // 登录
  {
    path: "/login",
    name: "Login",
    component: () => import("./views/auth/login/index.vue"),
    meta: {
      title: "擎路-登录",
      pageName: "登录",
    },
  },
  // 小程序登录
  {
    path: "/minapp-login",
    name: "minappLogin",
    component: () => import("./views/auth/minapp/index.vue"),
    meta: {
      title: "重新登录",
      pageName: "扫码登录",
    },
  },
  {
    path: "/forgot-password",
    name: "ForgotPassword",
    component: () => import("./views/auth/forgot/index.vue"),
    meta: {
      title: "擎路-忘记密码",
      pageName: "忘记密码",
    },
  },
  {
    path: "/register",
    name: "Register",
    component: () => import("./views/auth/register/index.vue"),
    meta: {
      title: "擎路-注册",
      pageName: "注册",
    },
  },
  {
    path: "/no-auth",
    name: "NoAuth",
    component: () => import("./views/auth/no-auth/index.vue"),
    meta: {
      title: "擎路-无权限",
      pageName: "无权限",
    },
  },

  // 长租订单
  {
    path: "/long-order",
    name: "LongOrderList",
    component: () => import("./views/long-order/index.vue"),
    meta: {
      title: "擎路-订单列表",
      pageName: "订单列表",
      keepAlive: true,
      hold: ["LongOrderDetail"],
    },
  },
  {
    path: "/long-order/create",
    name: "LongOrderCreate",
    component: () => import("./views/long-order/create.vue"),
    meta: {
      title: "擎路-订单创建",
      pageName: "订单创建",
    },
  },
  {
    path: "/long-order/detail",
    name: "LongOrderDetail",
    component: () => import("./views/long-order/detail.vue"),
    meta: {
      title: "擎路-订单详情",
      pageName: "订单详情",
      hold: true,
    },
  },
  {
    path: "/long-order/arrange-vehicles",
    name: "LongOrderArrangeVehicles",
    component: () => import("./views/long-order/arrange-vehicles"),
    meta: {
      title: "擎路-排车",
      pageName: "排车",
    },
  },
  {
    path: "/long-order/task-manage",
    name: "LongOrderTaskManage",
    component: () => import("./views/long-order/task-manage"),
    meta: {
      title: "擎路-任务管理",
      pageName: "任务管理",
    },
  },
  {
    path: "/long-order/initiate-reimbursement",
    name: "LongOrderInitiateReimbursement",
    component: () => import("./views/long-order/initiate-reimbursement"),
    meta: {
      title: "擎路-发起报销",
      pageName: "发起报销",
    },
  },
  {
    path: "/long-order/fee-detail",
    name: "LongOrderFeeDetail",
    component: () => import("./views/long-order/fee-detail.vue"),
    meta: {
      title: "擎路-费用明细",
      pageName: "费用明细",
    },
  },
  {
    path: "/long-order/instalment-list",
    name: "LongOrderInstalmentList",
    component: () => import("./views/long-order/instalment-list.vue"),
    meta: {
      title: "擎路-收款计划",
      pageName: "收款计划",
    },
  },
  {
    path: "/long-order/flow-record",
    name: "LongOrderFlowRecord",
    component: () => import("./views/long-order/flow-record.vue"),
    meta: {
      title: "擎路-流水记录",
      pageName: "流水记录",
    },
  },
  {
    path: "/long-order/remark",
    name: "LongOrderRemark",
    component: () => import("./views/long-order/remark.vue"),
    meta: {
      title: "擎路-订单备注",
      pageName: "订单备注",
    },
  },
  {
    path: "/long-order/payment",
    name: "LongOrderPayment",
    component: () => import("./views/long-order/payment.vue"),
    meta: {
      title: "擎路-租车押金扣款",
      pageName: "租车押金扣款",
    },
  },
  {
    path: "/long-order/reissue",
    name: "LongOrderReissue",
    component: () => import("./views/long-order/reissue.vue"),
    meta: {
      title: "擎路-补录视频/图片",
      pageName: "补录视频/图片",
    },
  },
  {
    path: "/long-order/cancel",
    name: "LongOrderCancel",
    component: () => import("./views/long-order/cancel.vue"),
    meta: {
      title: "擎路-取消订单",
      pageName: "取消订单",
    },
  },
  {
    path: "/long-order/pickup-return-record",
    name: "LongOrderPickupReturnRecord",
    component: () => import("./views/long-order/pickup-return-record.vue"),
    meta: {
      title: "擎路-取还车记录",
      pageName: "取还车记录",
    },
  },
  {
    path: "/long-order/supplement",
    name: "LongOrderSupplement",
    component: () => import("./views/long-order/supplement.vue"),
    meta: {
      title: "擎路-补款",
      pageName: "补款",
    },
  },
  {
    path: "/long-order/pickup",
    name: "LongOrderPickUp",
    component: () => import("./views/long-order/pickup.vue"),
    meta: {
      title: "擎路-取车",
      pageName: "取车",
    },
  },
  {
    path: "/long-order/return",
    name: "LongOrderReturn",
    component: () => import("./views/long-order/return.vue"),
    meta: {
      title: "擎路-还车",
      pageName: "还车",
    },
  },
  {
    path: "/long-order/scan-upload",
    name: "LongOrderScanUpload",
    component: () => import("./views/long-order/scanUpload.vue"),
    meta: {
      title: "擎路-凭证上传",
      pageName: "凭证上传",
    },
  },

  // 订单
  {
    path: "/order",
    name: "OrderList",
    component: () => import("./views/order/index.vue"),
    meta: {
      title: "擎路-订单列表",
      pageName: "订单列表",
      keepAlive: true,
      hold: [
        "OrderReturn",
        "OrderPickUp",
        "OrderDetail",
        "OrderArrangeVehicles",
        "merchantVerification",
        "userPickup",
        "userReturn",
      ],
    },
  },
  {
    path: "/order/create",
    name: "OrderCreate",
    component: () => import("./views/order/create.vue"),
    meta: {
      title: "擎路-订单创建",
      pageName: "订单创建",
    },
  },
  {
    path: "/order/detail",
    name: "OrderDetail",
    component: () => import("./views/order/detail.vue"),
    meta: {
      title: "擎路-订单详情",
      pageName: "订单详情",
    },
  },
  {
    path: "/order/arrange-vehicles",
    name: "OrderArrangeVehicles",
    component: () => import("./views/order/arrange-vehicles"),
    meta: {
      title: "擎路-排车",
      pageName: "排车",
    },
  },
  {
    path: "/order/task-manage",
    name: "OrderTaskManage",
    component: () => import("./views/order/task-manage"),
    meta: {
      title: "擎路-任务管理",
      pageName: "任务管理",
      // keepAlive: true,
      hold: ["OrderDetail"],
    },
  },
  {
    path: "/order/initiate-reimbursement",
    name: "OrderInitiateReimbursement",
    component: () => import("./views/order/initiate-reimbursement"),
    meta: {
      title: "擎路-发起报销",
      pageName: "发起报销",
    },
  },
  {
    path: "/order/fee-detail",
    name: "OrderFeeDetail",
    component: () => import("./views/order/fee-detail.vue"),
    meta: {
      title: "擎路-费用明细",
      pageName: "费用明细",
    },
  },
  {
    path: "/order/remark",
    name: "OrderRemark",
    component: () => import("./views/order/remark.vue"),
    meta: {
      title: "擎路-订单备注",
      pageName: "订单备注",
    },
  },
  {
    path: "/order/payment",
    name: "OrderPayment",
    component: () => import("./views/order/payment.vue"),
    meta: {
      title: "擎路-租车押金扣款",
      pageName: "租车押金扣款",
    },
  },
  {
    path: "/order/reissue",
    name: "OrderReissue",
    component: () => import("./views/order/reissue.vue"),
    meta: {
      title: "擎路-补录视频/图片",
      pageName: "补录视频/图片",
    },
  },
  {
    path: "/order/cancel",
    name: "OrderCancel",
    component: () => import("./views/order/cancel.vue"),
    meta: {
      title: "擎路-取消订单",
      pageName: "取消订单",
    },
  },
  {
    path: "/order/pickup-return-record",
    name: "OrderPickupReturnRecord",
    component: () => import("./views/order/pickup-return-record.vue"),
    meta: {
      title: "擎路-取还车记录",
      pageName: "取还车记录",
    },
  },
  {
    path: "/order/supplement",
    name: "OrderSupplement",
    component: () => import("./views/order/supplement.vue"),
    meta: {
      title: "擎路-补款",
      pageName: "补款",
    },
  },
  {
    path: "/order/pickup",
    name: "OrderPickUp",
    component: () => import("./views/order/pickup.vue"),
    meta: {
      title: "擎路-取车",
      pageName: "取车",
    },
  },
  {
    path: "/order/return",
    name: "OrderReturn",
    component: () => import("./views/order/return.vue"),
    meta: {
      title: "擎路-还车",
      pageName: "还车",
    },
  },
  {
    path: "/order/scan-upload",
    name: "OrderScanUpload",
    component: () => import("./views/order/scanUpload.vue"),
    meta: {
      title: "擎路-凭证上传",
      pageName: "凭证上传",
    },
  },

  // 违章
  {
    path: "/illegal/create",
    name: "PreIllegalCreate",
    component: () => import("./views/illegal/create.vue"),
  },
  {
    path: "/illegal/record",
    name: "IllegalRecord",
    component: () => import("./views/illegal/record.vue"),
  },
  {
    path: "/illegal/refund",
    name: "IllegalRefund",
    component: () => import("./views/illegal/refund.vue"),
  },
  // 违章
  {
    path: "/illegal/create",
    name: "IllegalCreate",
    component: () => import("./views/illegal/create.vue"),
    meta: {
      title: "擎路-添加违章",
      pageName: "添加违章",
    },
  },
  {
    path: "/illegal/record",
    name: "IllegalRecord",
    component: () => import("./views/illegal/record.vue"),
    meta: {
      title: "擎路-违章记录",
      pageName: "违章记录",
    },
  },
  {
    path: "/illegal/refund",
    name: "IllegalRefund",
    component: () => import("./views/illegal/refund.vue"),
    meta: {
      title: "擎路-违章押金退款",
      pageName: "违章押金退款",
    },
  },

  // 车损
  {
    path: "/damage/create",
    name: "DamageCreate",
    component: () => import("./views/damage/create.vue"),
    meta: {
      title: "擎路-添加车损",
      pageName: "添加车损",
    },
  },
  {
    path: "/damage/record",
    name: "DamageRecord",
    component: () => import("./views/damage/record.vue"),
    meta: {
      title: "擎路-车损记录",
      pageName: "车损记录",
    },
  },
  {
    path: "/damage/refund",
    name: "DamageRefund",
    component: () => import("./views/damage/refund.vue"),
    meta: {
      title: "擎路-车损押金退款",
      pageName: "车损押金退款",
    },
  },

  // 续租
  {
    path: "/renewal/create",
    name: "RenewalCreate",
    component: () => import("./views/renewal/create.vue"),
    meta: {
      title: "擎路-发起续租",
      pageName: "发起续租",
    },
  },
  {
    path: "/renewal/record",
    name: "RenewalRecord",
    component: () => import("./views/renewal/record.vue"),
    meta: {
      title: "擎路-续租记录",
      pageName: "续租记录",
    },
  },

  // 个人
  {
    path: "/person/entry",
    name: "PersonEntry",
    component: () => import("./views/person/entry.vue"),
    meta: {
      title: "擎路-录入证件信息",
      pageName: "录入证件信息",
    },
  },
  {
    path: "/person/detail",
    name: "PersonDetail",
    component: () => import("./views/person/detail.vue"),
    meta: {
      title: "擎路-证件信息",
      pageName: "证件信息",
    },
  },

  // 账号管理
  {
    path: "/account",
    name: "Accounts",
    component: () => import("./views/account/accounts/index.vue"),
    meta: {
      title: "擎路-账号管理",
      pageName: "账号管理",
    },
  },
  {
    path: "/account/:id",
    name: "AccountDetail",
    component: () => import("./views/account/detail.vue"),
    meta: {
      title: "擎路-账号详情",
      pageName: "账号详情",
    },
  },
  {
    path: "/account/add",
    name: "AddAccount",
    component: () => import("./views/account/edit/index.vue"),
    meta: {
      title: "擎路-添加账号",
      pageName: "添加账号",
    },
  },
  {
    path: "/account/edit/:id",
    name: "EditAccount",
    component: () => import("./views/account/edit/index.vue"),
    meta: {
      title: "擎路-编辑账号",
      pageName: "编辑账号",
    },
  },

  // 权限管理
  {
    path: "/role",
    name: "Roles",
    component: () => import("./views/account/roles/index.vue"),
    meta: {
      title: "擎路-权限管理",
      pageName: "权限管理",
    },
  },
  {
    path: "/role/edit/:roleId",
    name: "RoleEdit",
    component: () => import("./views/account/roles/edit.vue"),
    meta: {
      title: "擎路-权限设置",
      pageName: "权限设置",
    },
  },

  // 库存管理
  {
    path: "/stock",
    name: "Stock",
    component: () => import("./views/stock/index.vue"),
    meta: {
      title: "擎路-库存管理",
      pageName: "库存管理",
    },
  },

  {
    path: "/inventory",
    name: "Inventory",
    component: () => import("./views/inventory/index.vue"),
    meta: {
      title: "库存视图",
      pageName: "库存视图",
      // keepAlive: true,
    },
  },

  {
    path: "/dataOverview",
    name: "dataOverview",
    component: () => import("./views/dataOverview/index.vue"),
    meta: {
      title: "数据概览",
      pageName: "数据概览",
      // hold: ['dataOverviewSetting'],
    },
  },
  {
    path: "/wukongLoginCode",
    name: "wukongLoginCode",
    component: () => import("./views/wukongLoginCode/index.vue"),
    meta: {
      title: "悟空登录",
      pageName: "悟空登录",
    },
  },

  {
    path: "/wukongLogin",
    name: "wukongLogin",
    component: () => import("./views/wukongLogin/index.vue"),
    meta: {
      title: "悟空登录",
      pageName: "悟空登录",
    },
  },

  {
    path: "/channelManage",
    name: "channelManage",
    component: () => import("./views/channelManage/index.vue"),
    meta: {
      title: "渠道管理",
      pageName: "渠道管理",
    },
  },
  {
    path: "/vehicleControl",
    name: "vehicleControl",
    component: () => import("./views/vehicleControl/index.vue"),
    meta: {
      title: "车辆控制",
      pageName: "车辆控制",
      keepAlive: true,
      hold: ["vehicleControlDetails"],
    },
  },

  {
    path: "/selfPickupReturn/merchantVerification",
    name: "merchantVerification",
    component: () =>
      import("./views/order/selfPickupReturn/merchantVerification.vue"),
    meta: {
      title: "商户验车",
      pageName: "商户验车",
    },
  },
  {
    path: "/selfPickupReturn/userPickup",
    name: "userPickup",
    component: () => import("./views/order/selfPickupReturn/userPickup.vue"),
    meta: {
      title: "用户取车",
      pageName: "用户取车",
    },
  },
  {
    path: "/selfPickupReturn/userReturn",
    name: "userReturn",
    component: () => import("./views/order/selfPickupReturn/userReturn.vue"),
    meta: {
      title: "用户还车",
      pageName: "用户还车",
      // hold: true,
    },
  },
  {
    path: "/vehicleControlDetails",
    name: "vehicleControlDetails",
    component: () => import("./views/vehicleControlDetails/index.vue"),
    meta: {
      title: "车辆控制详情",
      pageName: "车辆控制详情",
    },
  },

  {
    path: "/stock/choose-store",
    name: "StockChooseStore",
    component: () => import("./views/stock/choose-store.vue"),
    meta: {
      title: "库存管理",
      pageName: "库存管理",
    },
  },

  {
    path: "/stock/summary/:storeId/:modelId",
    name: "StockSummary",
    component: () => import("./views/stock/summary.vue"),
    meta: {
      title: "擎路-库存汇总",
      pageName: "库存汇总",
    },
  },

  // 用户协议
  {
    path: "/privacy-policy",
    name: "PrivacyPolicy",
    component: () => import("./views/agreement/privacy-policy.vue"),
    meta: {
      title: "擎路-个人信息保护政策",
      pageName: "个人信息保护政策",
    },
  },
  {
    path: "/service-agreement",
    name: "ServiceAgreement",
    component: () => import("./views/agreement/service-agreement.vue"),
    meta: {
      title: "擎路-服务协议的确认和接纳",
      pageName: "服务协议的确认和接纳",
    },
  },
  {
    path: "/cancel-account",
    name: "CancelAccount",
    component: () => import("./views/agreement/cancel-account.vue"),
    meta: {
      title: "擎路-账户注销协议",
      pageName: "账户注销协议",
    },
  },

  // App 对接 Demo
  {
    path: "/demo",
    name: "Demo",
    component: () => import("./views/demo/index.vue"),
    meta: {
      title: "测试页",
      pageName: "App 测试页",
    },
  },

  // 价格
  {
    path: "/price",
    name: "PriceList",
    component: () => import("./views/price/index.vue"),
    meta: {
      title: "擎路-价格",
      pageName: "价格",
      keepAlive: true,
      hold: ["PriceSetting"],
    },
  },
  {
    path: "/price/setting",
    name: "PriceSetting",
    component: () => import("./views/price/setting.vue"),
    meta: {
      title: "擎路-价格设置",
      pageName: "价格设置",
      hold: true,
    },
  },
  {
    path: "/price/calendar",
    name: "PriceCalendar",
    component: () => import("./views/price/calendar.vue"),
    meta: {
      title: "擎路-价格日历",
      pageName: "价格日历",
    },
  },

  {
    path: "/tickets",
    name: "Tickets",
    component: () => import("./views/tickets/index.vue"),
    meta: {
      title: "擎路-工单管理",
      pageName: "工单管理",
    },
  },
  {
    path: "/tickets/shunting",
    name: "Shunting",
    component: () => import("./views/tickets/shunting.vue"),
    meta: {
      title: "擎路-调车单",
      pageName: "调车单",
    },
  },
  {
    path: "/tickets/shunting/detail",
    name: "ShuntingDetail",
    component: () => import("./views/tickets/shunting-detail.vue"),
    meta: {
      title: "擎路-调车单详情",
      pageName: "调车单详情",
    },
  },
  {
    path: "/tickets/shunting/create",
    name: "CreateShunting",
    component: () => import("./views/tickets/shunting-edit.vue"),
    meta: {
      title: "擎路-创建调车单",
      pageName: "创建调车单",
    },
  },
  {
    path: "/tickets/shunting/handle",
    name: "HandleShunting",
    component: () => import("./views/tickets/shunting-edit.vue"),
    meta: {
      title: "擎路-处理调车单",
      pageName: "处理调车单",
    },
  },
  {
    path: "/tickets/maintaince",
    name: "Maintaince",
    component: () => import("./views/tickets/maintaince.vue"),
    meta: {
      title: "擎路-维保单",
      pageName: "维保单",
    },
  },
  {
    path: "/tickets/maintaince/detail",
    name: "MaintainceDetail",
    component: () => import("./views/tickets/maintaince-detail.vue"),
    meta: {
      title: "擎路-维保单详情",
      pageName: "维保单详情",
    },
  },
  {
    path: "/tickets/maintaince/create",
    name: "CreateMaintaince",
    component: () => import("./views/tickets/maintaince-edit.vue"),
    meta: {
      title: "擎路-创建维保单",
      pageName: "创建维保单",
    },
  },
  {
    path: "/tickets/maintaince/handle",
    name: "HandleMaintaince",
    component: () => import("./views/tickets/maintaince-edit.vue"),
    meta: {
      title: "擎路-处理维保单",
      pageName: "处理维保单",
    },
  },
  {
    path: "/tickets/yearly",
    name: "Yearly",
    component: () => import("./views/tickets/yearly.vue"),
    meta: {
      title: "擎路-年检单",
      pageName: "年检单",
    },
  },
  {
    path: "/tickets/yearly/detail",
    name: "YearlyDetail",
    component: () => import("./views/tickets/yearly-detail.vue"),
    meta: {
      title: "擎路-年检单详情",
      pageName: "年检单详情",
    },
  },
  {
    path: "/tickets/yearly/create",
    name: "CreateYearly",
    component: () => import("./views/tickets/yearly-edit.vue"),
    meta: {
      title: "擎路-创建年检单",
      pageName: "创建年检单",
    },
  },

  // 违章管理
  {
    path: "/illegals",
    name: "IllegalManage",
    component: () => import("./views/illegal-manage/list/index.vue"),
    meta: {
      title: "擎路-违章管理",
      pageName: "违章管理",
    },
  },
  {
    path: "/illegals/detail",
    name: "IllegalDetail",
    component: () => import("./views/illegal-manage/detail.vue"),
    meta: {
      title: "擎路-违章详情",
      pageName: "违章详情",
    },
  },
  {
    path: "/illegals/handle",
    name: "IllegalHandle",
    component: () => import("./views/illegal-manage/handle.vue"),
    meta: {
      title: "擎路-处理违章",
      pageName: "处理违章",
    },
  },
  {
    path: "/illegals/create",
    name: "IllegalCreate",
    component: () => import("./views/illegal-manage/create.vue"),
    meta: {
      title: "擎路-新增违章",
      pageName: "新增违章",
    },
  },

  {
    path: "/test",
    name: "Test",
    component: () => import("./views/test.vue"),
    meta: {
      title: "擎路-测试",
      pageName: "测试",
    },
  },
  {
    path: "/test/:id",
    name: "TestId",
    component: () => import("./views/test.vue"),
    meta: {
      title: "擎路-测试",
      pageName: "测试",
    },
  },
  {
    path: "/native-test",
    name: "native-test",
    component: () => import("./views/native-test.vue"),
    meta: {
      title: "擎路-native测试",
      pageName: "native测试",
    },
  },
];

const needHold = (to, from) => {
  if (TypeChecker.isBoolean(to.meta.hold)) {
    return to.meta.hold;
  }

  if (TypeChecker.isArray(to.meta.hold)) {
    return to.meta.hold.includes(from.name);
  }

  return false;
};

const router = createRouter({
  history: createWebHashHistory(),
  routes,
  scrollBehavior: (to, from, savePosition) => {
    if (savePosition && needHold(to, from)) {
      return savePosition;
    }
    return { top: 0 };
  },
});

export default router;
```
