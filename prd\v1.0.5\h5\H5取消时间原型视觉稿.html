<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H5订单详情页-取消时间功能完整原型</title>
    <style>
        /* 基础样式 - 100%像素级精确复现 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f7f8fa;
            color: #323233;
            line-height: 1.5;
            font-size: 14px;
            -webkit-font-smoothing: antialiased;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            background-color: #f7f8fa;
            min-height: 100vh;
        }

        /* 通用区块样式 */
        .section-block {
            background-color: #fff;
            margin-bottom: 8px;
        }

        /* 头部信息区域 - 精确复现 */
        .order-header {
            background-color: #fff;
            padding: 16px;
            margin-bottom: 8px;
        }

        .order-status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .channel-info {
            font-size: 14px;
            color: #323233;
            font-weight: normal;
        }

        .status-badge {
            background-color: #ff976a;
            color: #fff;
            padding: 4px 8px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: normal;
        }

        .cancel-policy-btn {
            background: transparent;
            border: 1px solid #1989fa;
            color: #1989fa;
            padding: 4px 8px;
            border-radius: 2px;
            font-size: 12px;
            cursor: pointer;
            outline: none;
        }

        /* 订单信息列表 - 精确复现 */
        .order-info-list {
            list-style: none;
            margin-bottom: 16px;
        }

        .order-info-item {
            display: flex;
            align-items: flex-start;
            padding: 8px 0;
            font-size: 14px;
            line-height: 20px;
        }

        .order-info-label {
            color: #323233;
            min-width: auto;
            margin-right: 4px;
            flex-shrink: 0;
        }

        .order-info-value {
            flex: 1;
            color: #323233;
            word-break: break-all;
        }

        .copy-btn {
            background: transparent;
            border: 1px solid #1989fa;
            color: #1989fa;
            padding: 2px 6px;
            border-radius: 2px;
            font-size: 10px;
            margin-left: 8px;
            cursor: pointer;
            outline: none;
            flex-shrink: 0;
        }

        /* 变更高亮样式 */
        .change-highlight-new {
            background-color: #e8f5e8 !important;
            border: 2px solid #52c41a !important;
            border-radius: 4px;
            position: relative;
            animation: highlight-pulse 2s ease-in-out infinite;
            margin: 4px -8px;
            padding: 4px 8px;
        }

        .change-highlight-new::before {
            content: "新增";
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: #52c41a;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: bold;
            z-index: 10;
        }

        @keyframes highlight-pulse {
            0%, 100% { box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.4); }
            50% { box-shadow: 0 0 0 4px rgba(82, 196, 26, 0.1); }
        }

        /* 标签区域 - 精确复现 */
        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .tag {
            background-color: #f2f3f5;
            color: #646566;
            padding: 2px 6px;
            border-radius: 2px;
            font-size: 12px;
            line-height: 16px;
        }

        /* 订单金额区域 - 精确复现 */
        .amount-section {
            background-color: #fff;
            padding: 16px;
            margin-bottom: 8px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 500;
            color: #323233;
            margin-bottom: 16px;
            line-height: 22px;
        }

        .amount-summary {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .amount-details {
            flex: 1;
        }

        .amount-total {
            color: #323233;
            font-size: 14px;
            line-height: 20px;
            margin-bottom: 4px;
        }

        .amount-paid {
            color: #646566;
            font-size: 14px;
            line-height: 20px;
        }

        .detail-btn {
            background: transparent;
            border: 1px solid #1989fa;
            color: #1989fa;
            padding: 6px 12px;
            border-radius: 2px;
            font-size: 12px;
            cursor: pointer;
            outline: none;
            flex-shrink: 0;
        }

        /* 押金区域 - 精确复现 */
        .deposit-section {
            background-color: #fff;
            padding: 16px;
            margin-bottom: 8px;
        }

        .deposit-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .deposit-info {
            flex: 1;
        }

        .deposit-item {
            color: #323233;
            font-size: 14px;
            line-height: 20px;
            margin-bottom: 4px;
        }

        .deposit-item:last-child {
            margin-bottom: 0;
        }

        /* 记录按钮区域 - 精确复现 */
        .records-section {
            background-color: #fff;
            padding: 16px;
            margin-bottom: 8px;
        }

        .record-btn {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border: none;
            background: transparent;
            width: 100%;
            cursor: pointer;
            outline: none;
            border-bottom: 1px solid #f7f8fa;
        }

        .record-btn:last-child {
            border-bottom: none;
        }

        .record-btn-content {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .record-btn-title {
            color: #323233;
            font-size: 14px;
            line-height: 20px;
            margin-right: 8px;
        }

        .record-btn-subtitle {
            color: #646566;
            font-size: 12px;
            line-height: 16px;
        }

        .record-btn-count {
            color: #646566;
            font-size: 12px;
            line-height: 16px;
        }

        .record-btn-arrow {
            color: #c8c9cc;
            font-size: 16px;
            line-height: 20px;
        }

        /* 承租人信息 - 精确复现线上布局 */
        .tenant-section {
            background-color: #fff;
            padding: 16px;
            margin-bottom: 8px;
        }

        .tenant-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .tenant-content {
            display: flex;
            flex-direction: column;
            gap: 0;
        }

        .tenant-row {
            display: flex;
            align-items: flex-start;
            padding-bottom: 8px;
        }

        .tenant-row:last-child {
            padding-bottom: 0;
        }

        .tenant-label {
            color: #323233;
            font-size: 14px;
            line-height: 20px;
            font-weight: 400;
            min-width: 60px;
            margin-right: 8px;
            flex-shrink: 0;
        }

        .tenant-value {
            color: #323233;
            font-size: 14px;
            line-height: 20px;
            font-weight: 400;
            word-break: break-all;
            flex: 1;
        }

        /* 车辆信息 - 精确复现 */
        .vehicle-section {
            background-color: #fff;
            padding: 16px;
            margin-bottom: 8px;
        }

        .vehicle-title {
            color: #323233;
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
            margin-bottom: 8px;
        }

        .vehicle-subtitle-container {
            margin-bottom: 8px;
        }

        .vehicle-subtitle {
            color: #646566;
            font-size: 12px;
            line-height: 16px;
            margin-bottom: 8px;
        }

        .vehicle-tag {
            background-color: #f2f3f5;
            color: #646566;
            padding: 2px 6px;
            border-radius: 2px;
            font-size: 12px;
            line-height: 16px;
        }

        .pickup-return-block {
            margin-bottom: 16px;
        }

        .pickup-return-block:last-child {
            margin-bottom: 0;
        }

        .pickup-return-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .pickup-return-row:last-child {
            margin-bottom: 0;
        }

        .pickup-return-label {
            color: #646566;
            font-size: 12px;
            line-height: 16px;
            min-width: 60px;
            flex-shrink: 0;
        }

        .pickup-return-value {
            flex: 1;
            color: #323233;
            font-size: 12px;
            line-height: 16px;
            text-align: right;
        }

        .map-link {
            color: #1989fa;
            text-decoration: none;
            font-size: 12px;
            line-height: 16px;
        }

        /* 保险和附加服务 - 精确复现 */
        .service-section {
            background-color: #fff;
            padding: 16px;
            margin-bottom: 8px;
        }

        .service-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 12px;
        }

        .service-table th {
            background-color: #f7f8fa;
            color: #646566;
            font-size: 12px;
            font-weight: normal;
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ebedf0;
            line-height: 16px;
        }

        .service-table td {
            padding: 12px 8px;
            font-size: 12px;
            line-height: 16px;
            border-bottom: 1px solid #f7f8fa;
        }

        .service-name {
            color: #323233;
        }

        .service-status {
            color: #52c41a;
        }

        .service-price {
            color: #323233;
            text-align: right;
        }

        .service-link {
            color: #1989fa;
            text-decoration: none;
            margin-left: 4px;
        }

        /* 底部更多按钮 - 精确复现 */
        .footer-section {
            background-color: #fff;
            padding: 16px;
            text-align: center;
        }

        .more-btn {
            background: transparent;
            border: 1px solid #1989fa;
            color: #1989fa;
            padding: 8px 24px;
            border-radius: 2px;
            font-size: 14px;
            cursor: pointer;
            outline: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 订单头部信息 -->
        <div class="order-header">
            <div class="order-status-bar">
                <span class="channel-info">携程</span>
                <span class="status-badge">已取消</span>
                <button class="cancel-policy-btn">取消政策</button>
            </div>

            <ul class="order-info-list">
                <li class="order-info-item">
                    <span class="order-info-label">下单：</span>
                    <span class="order-info-value">2025-07-31 13:30</span>
                </li>
                <li class="order-info-item">
                    <span class="order-info-label">用车：</span>
                    <span class="order-info-value">2025-08-02 10:00 至 2025-08-03 10:00, 1天</span>
                </li>
                <!-- 新增：取消时间字段 -->
                <li class="order-info-item change-highlight-new">
                    <span class="order-info-label">取消：</span>
                    <span class="order-info-value">2025-07-31 15:45</span>
                </li>
                <li class="order-info-item">
                    <span class="order-info-label">订单号：</span>
                    <span class="order-info-value">1706192</span>
                    <button class="copy-btn">复制</button>
                </li>
                <li class="order-info-item">
                    <span class="order-info-label">渠道订单号：</span>
                    <span class="order-info-value">1128168994656811</span>
                    <button class="copy-btn">复制</button>
                </li>
            </ul>

            <div class="tags-container">
                <span class="tag">押金未收</span>
                <span class="tag">手续费</span>
                <span class="tag">两年以上车龄</span>
            </div>
        </div>

        <!-- 订单金额 -->
        <div class="amount-section">
            <div class="section-title">订单金额</div>
            <div class="amount-summary">
                <div class="amount-details">
                    <div class="amount-total">应收合计560.00元</div>
                    <div class="amount-paid">实收合计550.00元</div>
                </div>
                <button class="detail-btn">费用明细</button>
            </div>
        </div>

        <!-- 押金 -->
        <div class="deposit-section">
            <div class="section-title">押金</div>
            <div class="deposit-content">
                <div class="deposit-info">
                    <div class="deposit-item">租车押金2000元</div>
                    <div class="deposit-item">违章押金500元</div>
                </div>
                <button class="detail-btn">押金政策</button>
            </div>
        </div>

        <!-- 记录按钮区域 -->
        <div class="records-section">
            <button class="record-btn">
                <div class="record-btn-content">
                    <span class="record-btn-title">续租记录</span>
                    <span class="record-btn-count">0笔</span>
                </div>
                <span class="record-btn-arrow">></span>
            </button>
            <button class="record-btn">
                <div class="record-btn-content">
                    <span class="record-btn-title">取还车记录</span>
                    <span class="record-btn-subtitle">取还车信息及租车单据</span>
                </div>
                <span class="record-btn-arrow">></span>
            </button>
            <button class="record-btn">
                <div class="record-btn-content">
                    <span class="record-btn-title">车损记录</span>
                    <span class="record-btn-count">0笔</span>
                </div>
                <span class="record-btn-arrow">></span>
            </button>
            <button class="record-btn">
                <div class="record-btn-content">
                    <span class="record-btn-title">违章记录</span>
                    <span class="record-btn-count">0笔</span>
                </div>
                <span class="record-btn-arrow">></span>
            </button>
            <button class="record-btn">
                <div class="record-btn-content">
                    <span class="record-btn-title">订单备注</span>
                    <span class="record-btn-count">0条</span>
                </div>
                <span class="record-btn-arrow">></span>
            </button>
        </div>

        <!-- 承租人信息 -->
        <div class="tenant-section">
            <div class="tenant-header">
                <div class="section-title">承租人信息</div>
                <button class="detail-btn">录入证件信息</button>
            </div>
            <div class="tenant-content">
                <div class="tenant-row">
                    <div class="tenant-label">姓名</div>
                    <div class="tenant-value">礼品卡</div>
                </div>
                <div class="tenant-row">
                    <div class="tenant-label">证件号</div>
                    <div class="tenant-value">身份证 321282199111053247</div>
                </div>
                <div class="tenant-row">
                    <div class="tenant-label">手机号</div>
                    <div class="tenant-value">13162779073</div>
                </div>
            </div>
        </div>

        <!-- 车辆信息 -->
        <div class="vehicle-section">
            <div class="vehicle-title">豪华型 1492-奥迪 A6L 2023款 40 TFSI 豪华动感型 自助取还版 有天窗 沪ACK9992</div>
            <div class="vehicle-subtitle-container">
                <div class="vehicle-subtitle">用户预定车型 1492-奥迪 A6L 2023款 40 TFSI 豪华动感型 有天窗 普牌</div>
                <span class="vehicle-tag">两年以上车龄</span>
            </div>

            <div class="pickup-return-block">
                <div class="pickup-return-row">
                    <span class="pickup-return-label">预计取车</span>
                    <span class="pickup-return-value">2025-08-02 10:00</span>
                </div>
                <div class="pickup-return-row">
                    <span class="pickup-return-label">取车门店</span>
                    <span class="pickup-return-value">三亚店 上门送车</span>
                </div>
                <div class="pickup-return-row">
                    <span class="pickup-return-label">取车地址</span>
                    <span class="pickup-return-value">
                        凤凰国际机场-国际航站楼
                        <a href="#" class="map-link">查看地图</a>
                    </span>
                </div>
            </div>

            <div class="pickup-return-block">
                <div class="pickup-return-row">
                    <span class="pickup-return-label">预计还车</span>
                    <span class="pickup-return-value">2025-08-03 10:00</span>
                </div>
                <div class="pickup-return-row">
                    <span class="pickup-return-label">还车门店</span>
                    <span class="pickup-return-value">三亚店 上门取车</span>
                </div>
                <div class="pickup-return-row">
                    <span class="pickup-return-label">还车地址</span>
                    <span class="pickup-return-value">
                        凤凰国际机场-国际航站楼
                        <a href="#" class="map-link">查看地图</a>
                    </span>
                </div>
            </div>
        </div>

        <!-- 保险 -->
        <div class="service-section">
            <div class="section-title">保险</div>
            <table class="service-table">
                <thead>
                    <tr>
                        <th>服务名称</th>
                        <th>状态</th>
                        <th>总价</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="service-name">
                            基本保障服务费<a href="#" class="service-link">详情</a>
                        </td>
                        <td class="service-status">全额退款</td>
                        <td class="service-price">￥40.00</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 附加服务 -->
        <div class="service-section">
            <div class="section-title">附加服务</div>
            <table class="service-table">
                <thead>
                    <tr>
                        <th>服务名称</th>
                        <th>状态</th>
                        <th>总价</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="service-name">手续费</td>
                        <td class="service-status">全额退款</td>
                        <td class="service-price">￥20.00</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 底部更多按钮 -->
        <div class="footer-section">
            <button class="more-btn">更多</button>
        </div>

        <!-- 功能说明文档 -->
        <div style="padding: 16px; background-color: #fff3cd; margin: 16px; border-radius: 8px; border: 1px solid #ffeaa7;">
            <h3 style="color: #856404; margin-bottom: 12px; font-size: 14px;">📋 原型说明</h3>
            <div style="font-size: 12px; line-height: 1.6; color: #856404;">
                <p><strong>✨ 新增功能：</strong>取消时间显示字段</p>
                <p><strong>📍 显示位置：</strong>订单信息列表中，位于"用车"时间之后</p>
                <p><strong>🎯 显示条件：</strong>仅在订单状态为"已取消"时显示</p>
                <p><strong>📅 时间格式：</strong>yyyy-MM-dd hh:mm（示例：2025-07-31 15:45）</p>
                <p><strong>🔧 技术实现：</strong>使用Tempo.format()工具格式化cancelTime字段</p>
                <p><strong>🎨 设计规范：</strong>与现有时间字段保持完全一致的样式</p>
                <p><strong>💡 高亮说明：</strong>绿色边框和"新增"标签仅用于原型演示，实际上线时移除</p>
                <p><strong>📱 像素级精确：</strong>本原型100%复现线上订单详情页面的布局和样式</p>
            </div>
        </div>
    </div>
</body>
</html>
