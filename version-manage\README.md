# 版本管理系统

## 概述

本版本管理系统用于跟踪和管理 PRD 生成项目的版本信息、变更记录和生成的文档。

## 文件结构

```
version-manage/
├── version.json          # 版本配置和记录
├── CHANGELOG.md         # 版本变更日志
├── README.md           # 本说明文档
└── scripts/            # 版本管理脚本（预留）
```

## 版本规范

### 版本号格式

遵循语义化版本控制（Semantic Versioning）：

- **主版本号（Major）**：不兼容的 API 修改
- **次版本号（Minor）**：向下兼容的功能性新增
- **修订号（Patch）**：向下兼容的问题修正

格式：`v{major}.{minor}.{patch}`
示例：`v1.0.0`, `v1.2.3`, `v2.0.0`

### 版本类型

- **发布版本**：`v1.0.0`
- **预发布版本**：`v1.0.0-alpha.1`, `v1.0.0-beta.2`, `v1.0.0-rc.1`
- **开发版本**：`v1.0.0-dev.20241201`

## 版本管理操作

### 1. 查看当前版本

```bash
# 查看version.json文件中的currentVersion字段
cat version-manage/version.json | grep "currentVersion"
```

### 2. 创建新版本

1. 更新 `version.json` 文件
2. 在 `versions` 数组中添加新版本信息
3. 更新 `currentVersion` 字段
4. 创建对应的文件夹结构：
   ```bash
   mkdir -p prd/{新版本号}/h5 prd/{新版本号}/pc
   mkdir -p prompt/{新版本号}/h5 prompt/{新版本号}/pc
   ```

### 3. 记录 PRD 生成

每次生成 PRD 后，需要在对应版本的记录中更新：

- `prdGenerated.h5` 或 `prdGenerated.pc` 数组
- 添加生成的 PRD 文件信息

### 4. 记录提示词生成

每次生成开发提示词后，需要在对应版本的记录中更新：

- `promptsGenerated.h5` 或 `promptsGenerated.pc` 数组
- 添加生成的提示词文件信息

## 版本记录格式

### version.json 结构说明

```json
{
  "currentVersion": "当前版本号",
  "versions": [
    {
      "version": "版本号",
      "date": "发布日期",
      "description": "版本描述",
      "changes": ["变更列表"],
      "prdGenerated": {
        "h5": ["H5项目的PRD文件列表"],
        "pc": ["PC项目的PRD文件列表"]
      },
      "promptsGenerated": {
        "h5": ["H5项目的提示词文件列表"],
        "pc": ["PC项目的提示词文件列表"]
      }
    }
  ],
  "versionFormat": "版本号格式",
  "autoIncrement": {
    "enabled": "是否启用自动递增",
    "type": "递增类型（major/minor/patch）"
  },
  "branches": {
    "main": "主分支版本",
    "develop": "开发分支版本"
  }
}
```

## 工作流程

### PRD 生成工作流

1. **确定版本号**

   - 新项目：创建新的次版本号
   - 修复/优化：递增修订号
   - 重大变更：递增主版本号

2. **准备资源**

   - 检查组件库版本
   - 确认项目架构文档
   - 验证路由文件

3. **生成 PRD**

   - 使用全局 PRD 生成提示词
   - 保存到 `prd/{版本号}/{项目类型}/`
   - 更新版本记录

4. **生成开发提示词**

   - 基于 PRD 生成对应的开发提示词
   - 保存到 `prompt/{版本号}/{项目类型}/`
   - 更新版本记录

5. **更新变更日志**
   - 在 `CHANGELOG.md` 中记录变更
   - 提交代码到 git 仓库

## 最佳实践

### 版本管理

1. **定期备份**：定期备份 `version.json` 文件
2. **变更追踪**：每次变更都要详细记录
3. **版本标签**：在 git 中为每个版本创建标签
4. **文档同步**：确保文档版本与代码版本同步

### 文件组织

1. **命名规范**：严格遵循文件命名规范
2. **目录结构**：保持目录结构的一致性
3. **文件清理**：定期清理过时的版本文件
4. **权限管理**：合理设置文件访问权限

## 故障排除

### 常见问题

1. **版本冲突**：检查 `version.json` 中是否有重复版本号
2. **文件缺失**：确认所有必需的资源文件都存在
3. **格式错误**：验证 JSON 文件格式是否正确
4. **权限问题**：检查文件和目录的读写权限

### 恢复操作

1. **版本回滚**：从 git 历史中恢复到指定版本
2. **文件恢复**：从备份中恢复丢失的文件
3. **重新生成**：基于现有资源重新生成 PRD 和提示词

---

**注意：修改版本信息前，请务必备份相关文件，并确保所有团队成员都了解版本变更。**
