<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短租订单列表复制功能 - 擎路PC</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd@5.12.8/dist/reset.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd@5.12.8/dist/antd.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@ant-design/icons@5.2.6/lib/index.css">
    <style>
        /* 页面特定样式，确保与线上系统完全一致 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Not<PERSON>', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }

        .layout {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 200px;
            background-color: #001529;
            color: white;
            padding: 20px 0;
        }

        .main-content {
            flex: 1;
            background-color: #f0f2f5;
        }

        .header {
            background-color: white;
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #f0f0f0;
        }

        .content {
            padding: 24px;
        }

        .search-form {
            background-color: white;
            padding: 24px;
            border-radius: 6px;
            margin-bottom: 16px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }

        .table-container {
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }

        .table-header {
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-tabs {
            display: flex;
            gap: 8px;
        }

        .table-tab {
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            color: #666;
            border: 1px solid transparent;
        }

        .table-tab.active {
            background-color: #e6f7ff;
            color: #1890ff;
            border-color: #91d5ff;
        }

        .table-actions {
            display: flex;
            gap: 8px;
        }

        .ant-table {
            font-size: 14px;
        }

        .ant-table-thead > tr > th {
            background-color: #fafafa;
            font-weight: 500;
            color: #262626;
            border-bottom: 1px solid #f0f0f0;
        }

        .ant-table-tbody > tr > td {
            border-bottom: 1px solid #f0f0f0;
            padding: 12px 16px;
        }

        .ant-table-tbody > tr:hover > td {
            background-color: #f5f5f5;
        }

        /* 复制图标样式 */
        .copy-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 16px;
            height: 16px;
            margin-left: 4px;
            cursor: pointer;
            color: #666;
            transition: color 0.3s;
        }

        .copy-icon:hover {
            color: #1890ff;
        }

        .copy-icon svg {
            width: 14px;
            height: 14px;
        }

        /* 改动点标注样式 */
        .change-highlight {
            background-color: #fff7e6;
            border: 2px solid #ffa940;
            border-radius: 4px;
            padding: 2px;
            position: relative;
        }

        .change-highlight::after {
            content: "新增复制功能";
            position: absolute;
            top: -8px;
            left: 8px;
            background-color: #ffa940;
            color: white;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 2px;
            white-space: nowrap;
        }

        /* 订单信息样式 */
        .order-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .order-number {
            display: flex;
            align-items: center;
            gap: 4px;
            font-weight: 500;
        }

        .channel-order-number {
            display: flex;
            align-items: center;
            gap: 4px;
            color: #666;
            font-size: 13px;
        }

        .plate-number {
            display: flex;
            align-items: center;
            gap: 4px;
            font-weight: 500;
            color: #1890ff;
        }

        /* 按钮样式 */
        .ant-btn {
            border-radius: 4px;
            font-size: 14px;
            height: 32px;
            padding: 4px 15px;
        }

        .ant-btn-primary {
            background-color: #1890ff;
            border-color: #1890ff;
        }

        .ant-btn-default {
            background-color: white;
            border-color: #d9d9d9;
            color: #262626;
        }

        /* 搜索表单样式 */
        .search-row {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
            flex-wrap: wrap;
        }

        .search-item {
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 200px;
        }

        .search-label {
            font-size: 14px;
            color: #262626;
            white-space: nowrap;
        }

        .ant-input {
            border-radius: 4px;
            border: 1px solid #d9d9d9;
            padding: 4px 11px;
            font-size: 14px;
        }

        .ant-select {
            min-width: 120px;
        }

        /* 分页样式 */
        .pagination {
            padding: 16px 24px;
            text-align: right;
            border-top: 1px solid #f0f0f0;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .search-row {
                flex-direction: column;
            }
            
            .search-item {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div style="padding: 0 16px; margin-bottom: 24px;">
                <h3 style="color: white; margin: 0;">擎路管理系统</h3>
            </div>
            <div style="padding: 8px 16px; background-color: #1890ff; color: white; margin: 0 16px 8px;">
                订单
            </div>
            <div style="padding: 8px 16px; color: #1890ff; margin: 0 16px;">
                短租订单
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 头部 -->
            <div class="header">
                <div style="display: flex; align-items: center; gap: 16px;">
                    <h2 style="margin: 0; font-size: 18px; font-weight: 500;">短租订单</h2>
                </div>
                <div style="display: flex; align-items: center; gap: 16px;">
                    <span>企鹅123</span>
                    <button class="ant-btn ant-btn-default">退出</button>
                </div>
            </div>

            <!-- 内容区 -->
            <div class="content">
                <!-- 搜索表单 -->
                <div class="search-form">
                    <div class="search-row">
                        <div class="search-item">
                            <span class="search-label">订单信息:</span>
                            <input type="text" class="ant-input" placeholder="请填写">
                        </div>
                        <div class="search-item">
                            <span class="search-label">订单来源:</span>
                            <select class="ant-input">
                                <option>全部</option>
                                <option>订单</option>
                                <option>携程订单</option>
                                <option>摩捷订单</option>
                            </select>
                        </div>
                        <div class="search-item">
                            <span class="search-label">车型:</span>
                            <input type="text" class="ant-input" placeholder="ID/名称">
                        </div>
                    </div>
                    <div class="search-row">
                        <div class="search-item">
                            <span class="search-label">订单状态:</span>
                            <div style="display: flex; gap: 8px; align-items: center;">
                                <label style="display: flex; align-items: center; gap: 4px;">
                                    <input type="checkbox" checked> 已确认
                                </label>
                                <label style="display: flex; align-items: center; gap: 4px;">
                                    <input type="checkbox" checked> 已排车
                                </label>
                                <label style="display: flex; align-items: center; gap: 4px;">
                                    <input type="checkbox" checked> 已取车
                                </label>
                                <label style="display: flex; align-items: center; gap: 4px;">
                                    <input type="checkbox" checked> 已还车
                                </label>
                            </div>
                        </div>
                        <div class="search-item">
                            <span class="search-label">订单标签:</span>
                            <select class="ant-input">
                                <option>全部</option>
                                <option>押金未收</option>
                                <option>无忧租</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: flex; gap: 8px; justify-content: flex-end;">
                        <button class="ant-btn ant-btn-primary">查 询</button>
                        <button class="ant-btn ant-btn-default">重 置</button>
                    </div>
                </div>

                <!-- 表格容器 -->
                <div class="table-container">
                    <!-- 表格头部 -->
                    <div class="table-header">
                        <div class="table-tabs">
                            <div class="table-tab active">全部(188)</div>
                            <div class="table-tab">今日待取车(0)</div>
                            <div class="table-tab">今日待还车(0)</div>
                            <div class="table-tab">今日待排车(0)</div>
                            <div class="table-tab">逾期未取车(131)</div>
                            <div class="table-tab">逾期未还车(12)</div>
                        </div>
                        <div class="table-actions">
                            <button class="ant-btn ant-btn-primary">新增订单</button>
                            <button class="ant-btn ant-btn-default">导出订单</button>
                            <button class="ant-btn ant-btn-default">新增标签</button>
                        </div>
                    </div>

                    <!-- 表格 -->
                    <table class="ant-table" style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr>
                                <th style="text-align: left; padding: 12px 16px; background-color: #fafafa; border-bottom: 1px solid #f0f0f0;">
                                    创建时间
                                </th>
                                <th style="text-align: left; padding: 12px 16px; background-color: #fafafa; border-bottom: 1px solid #f0f0f0;">
                                    状态
                                </th>
                                <th style="text-align: left; padding: 12px 16px; background-color: #fafafa; border-bottom: 1px solid #f0f0f0;">
                                    预订车辆
                                </th>
                                <th style="text-align: left; padding: 12px 16px; background-color: #fafafa; border-bottom: 1px solid #f0f0f0;">
                                    自定义标签
                                </th>
                                <th style="text-align: left; padding: 12px 16px; background-color: #fafafa; border-bottom: 1px solid #f0f0f0;">
                                    取还
                                </th>
                                <th style="text-align: left; padding: 12px 16px; background-color: #fafafa; border-bottom: 1px solid #f0f0f0;">
                                    承租人
                                </th>
                                <th style="text-align: left; padding: 12px 16px; background-color: #fafafa; border-bottom: 1px solid #f0f0f0;">
                                    订单总价
                                </th>
                                <th style="text-align: left; padding: 12px 16px; background-color: #fafafa; border-bottom: 1px solid #f0f0f0;">
                                    操作
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 第一行数据 -->
                            <tr>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div class="order-info">
                                        <div>2025-07-18 10:25</div>
                                        <div>来源：订单</div>
                                        <div class="channel-order-number change-highlight">
                                            渠道订单号：-
                                            <span class="copy-icon" title="复制渠道订单号">
                                                <svg viewBox="64 64 896 896" fill="currentColor">
                                                    <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"/>
                                                    <path d="M704 192H192c-17.7 0-32 14.3-32 32v512c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM660 732H236V276h424v456z"/>
                                                </svg>
                                            </span>
                                        </div>
                                        <div class="order-number change-highlight">
                                            订单号1705946
                                            <span class="copy-icon" title="复制订单号">
                                                <svg viewBox="64 64 896 896" fill="currentColor">
                                                    <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"/>
                                                    <path d="M704 192H192c-17.7 0-32 14.3-32 32v512c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM660 732H236V276h424v456z"/>
                                                </svg>
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div>已取车</div>
                                    <div>取车：sjz-admin</div>
                                    <div>还车：待排司机</div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div class="plate-number change-highlight">
                                        琼BF09400
                                        <span class="copy-icon" title="复制车牌号">
                                            <svg viewBox="64 64 896 896" fill="currentColor">
                                                <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"/>
                                                <path d="M704 192H192c-17.7 0-32 14.3-32 32v512c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM660 732H236V276h424v456z"/>
                                            </svg>
                                        </span>
                                    </div>
                                    <div>1376-大众 迈腾GTE插电混动 2020款 GTE 豪华型 有天窗 普牌</div>
                                    <div style="color: #ff4d4f;">押金未收</div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <!-- 自定义标签 -->
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="font-weight: 500;">预计 2025-07-18 11:00 至 2025-07-31 17:00</div>
                                    <div style="color: #666;">13天 7小时</div>
                                    <div>取车：神风租车22(到店取车)</div>
                                    <div>云南普洱梅子湖公园公园分店</div>
                                    <div>还车：神风租车22(到店还车)</div>
                                    <div>云南普洱梅子湖公园公园分店</div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div>sjz-admin</div>
                                    <div>13627003158</div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="color: #ff4d4f; font-weight: 500;">￥29</div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                        <button class="ant-btn ant-btn-default" style="font-size: 12px; padding: 2px 8px;">排司机</button>
                                        <button class="ant-btn ant-btn-default" style="font-size: 12px; padding: 2px 8px;">还车</button>
                                        <button class="ant-btn ant-btn-default" style="font-size: 12px; padding: 2px 8px;">强制改排</button>
                                        <button class="ant-btn ant-btn-default" style="font-size: 12px; padding: 2px 8px;">添加标签</button>
                                    </div>
                                </td>
                            </tr>

                            <!-- 第二行数据 -->
                            <tr>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div class="order-info">
                                        <div>2025-07-15 16:17</div>
                                        <div>来源：线下渠道订单</div>
                                        <div class="channel-order-number change-highlight">
                                            渠道订单号：-
                                            <span class="copy-icon" title="复制渠道订单号">
                                                <svg viewBox="64 64 896 896" fill="currentColor">
                                                    <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"/>
                                                    <path d="M704 192H192c-17.7 0-32 14.3-32 32v512c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM660 732H236V276h424v456z"/>
                                                </svg>
                                            </span>
                                        </div>
                                        <div class="order-number change-highlight">
                                            订单号1705915
                                            <span class="copy-icon" title="复制订单号">
                                                <svg viewBox="64 64 896 896" fill="currentColor">
                                                    <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"/>
                                                    <path d="M704 192H192c-17.7 0-32 14.3-32 32v512c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM660 732H236V276h424v456z"/>
                                                </svg>
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div>已取车</div>
                                    <div>取车：sjz-admin</div>
                                    <div>还车：待排司机</div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div class="plate-number change-highlight">
                                        琼BF06517
                                        <span class="copy-icon" title="复制车牌号">
                                            <svg viewBox="64 64 896 896" fill="currentColor">
                                                <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"/>
                                                <path d="M704 192H192c-17.7 0-32 14.3-32 32v512c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM660 732H236V276h424v456z"/>
                                            </svg>
                                        </span>
                                    </div>
                                    <div>1376-大众 迈腾GTE插电混动 2020款 GTE 豪华型 有天窗 普牌</div>
                                    <div style="color: #ff4d4f;">押金未收</div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <!-- 自定义标签 -->
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="font-weight: 500;">预计 2025-07-15 16:17 至 2025-07-16 16:17</div>
                                    <div style="color: #666;">1天</div>
                                    <div>取车：神风租车22(到店取车)</div>
                                    <div>云南普洱梅子湖公园公园分店</div>
                                    <div>还车：神风租车22(到店还车)</div>
                                    <div>云南普洱梅子湖公园公园分店</div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div>sjz-admin</div>
                                    <div>13627003158</div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="color: #ff4d4f; font-weight: 500;">￥3</div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                        <button class="ant-btn ant-btn-default" style="font-size: 12px; padding: 2px 8px;">排司机</button>
                                        <button class="ant-btn ant-btn-default" style="font-size: 12px; padding: 2px 8px;">还车</button>
                                        <button class="ant-btn ant-btn-default" style="font-size: 12px; padding: 2px 8px;">强制改排</button>
                                        <button class="ant-btn ant-btn-default" style="font-size: 12px; padding: 2px 8px;">添加标签</button>
                                    </div>
                                </td>
                            </tr>

                            <!-- 第三行数据（携程订单示例） -->
                            <tr>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div class="order-info">
                                        <div>2025-05-07 19:59</div>
                                        <div>来源：携程订单</div>
                                        <div class="channel-order-number change-highlight">
                                            渠道订单号：1128168945146302
                                            <span class="copy-icon" title="复制渠道订单号">
                                                <svg viewBox="64 64 896 896" fill="currentColor">
                                                    <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"/>
                                                    <path d="M704 192H192c-17.7 0-32 14.3-32 32v512c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM660 732H236V276h424v456z"/>
                                                </svg>
                                            </span>
                                        </div>
                                        <div class="order-number change-highlight">
                                            订单号1704926
                                            <span class="copy-icon" title="复制订单号">
                                                <svg viewBox="64 64 896 896" fill="currentColor">
                                                    <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"/>
                                                    <path d="M704 192H192c-17.7 0-32 14.3-32 32v512c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM660 732H236V276h424v456z"/>
                                                </svg>
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div>已排车</div>
                                    <div>取车：待排司机</div>
                                    <div>还车：待排司机</div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div class="plate-number change-highlight">
                                        琼BD04610
                                        <span class="copy-icon" title="复制车牌号">
                                            <svg viewBox="64 64 896 896" fill="currentColor">
                                                <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"/>
                                                <path d="M704 192H192c-17.7 0-32 14.3-32 32v512c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM660 732H236V276h424v456z"/>
                                            </svg>
                                        </span>
                                    </div>
                                    <div>1380-别克 微蓝6 2020款 互联时尚型 PLUS 普牌</div>
                                    <div style="color: #ff4d4f;">押金未收</div>
                                    <div style="color: #52c41a;">无忧租</div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <!-- 自定义标签 -->
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="font-weight: 500;">预计 2025-07-01 10:00 至 2025-07-04 10:30</div>
                                    <div style="color: #666;">3天 1小时</div>
                                    <div>取车：神风租车22(到店取车)</div>
                                    <div>云南普洱梅子湖公园公园分店</div>
                                    <div>还车：神风租车22(到店还车)</div>
                                    <div>云南普洱梅子湖公园公园分店</div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div>徐艳</div>
                                    <div>17621905785</div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="color: #ff4d4f; font-weight: 500;">￥659</div>
                                </td>
                                <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0;">
                                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                        <button class="ant-btn ant-btn-default" style="font-size: 12px; padding: 2px 8px;">排司机</button>
                                        <button class="ant-btn ant-btn-default" style="font-size: 12px; padding: 2px 8px;">改排</button>
                                        <button class="ant-btn ant-btn-default" style="font-size: 12px; padding: 2px 8px;">取车</button>
                                        <button class="ant-btn ant-btn-default" style="font-size: 12px; padding: 2px 8px;">强制改排</button>
                                        <button class="ant-btn ant-btn-default" style="font-size: 12px; padding: 2px 8px;">添加标签</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- 分页 -->
                    <div class="pagination">
                        <div style="display: flex; align-items: center; gap: 8px; justify-content: center;">
                            <button class="ant-btn ant-btn-default" disabled style="padding: 4px 8px;">‹</button>
                            <button class="ant-btn ant-btn-primary" style="padding: 4px 8px;">1</button>
                            <button class="ant-btn ant-btn-default" style="padding: 4px 8px;">2</button>
                            <button class="ant-btn ant-btn-default" style="padding: 4px 8px;">3</button>
                            <button class="ant-btn ant-btn-default" style="padding: 4px 8px;">4</button>
                            <button class="ant-btn ant-btn-default" style="padding: 4px 8px;">5</button>
                            <button class="ant-btn ant-btn-default" style="padding: 4px 8px;">›</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 复制功能实现
        document.addEventListener('DOMContentLoaded', function() {
            const copyIcons = document.querySelectorAll('.copy-icon');
            
            copyIcons.forEach(icon => {
                icon.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 获取要复制的文本
                    const textToCopy = this.parentElement.textContent.replace('复制', '').trim();
                    
                    // 复制到剪贴板
                    navigator.clipboard.writeText(textToCopy).then(function() {
                        // 显示成功提示
                        const originalTitle = icon.getAttribute('title');
                        icon.setAttribute('title', '复制成功！');
                        
                        // 改变图标颜色
                        icon.style.color = '#52c41a';
                        
                        setTimeout(() => {
                            icon.setAttribute('title', originalTitle);
                            icon.style.color = '#666';
                        }, 2000);
                        
                        console.log('复制成功:', textToCopy);
                    }).catch(function(err) {
                        console.error('复制失败:', err);
                        alert('复制失败，请手动复制');
                    });
                });
            });
        });

        // 添加交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 表格行悬停效果
            const tableRows = document.querySelectorAll('.ant-table tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#f5f5f5';
                });
                
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                });
            });

            // 标签页切换效果
            const tabs = document.querySelectorAll('.table-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 按钮点击效果
            const buttons = document.querySelectorAll('.ant-btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    if (this.textContent.includes('查询') || this.textContent.includes('重置')) {
                        e.preventDefault();
                        console.log('点击了:', this.textContent);
                    }
                });
            });
        });
    </script>
</body>
</html> 