<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车辆资源检测 - 擎路SaaS管理系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.min.js"></script>
    <style>
        /* =======================================================
           CSS 重置和基础样式（100%复刻SaaS系统样式）
           ======================================================= */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #000000d9;
            font-size: 14px;
            line-height: 1.5;
        }

        /* =======================================================
           整体布局结构（精确复刻SaaS系统布局）
           ======================================================= */
        .layout {
            display: flex;
            min-height: 100vh;
        }

        /* 左侧导航栏 - 精确复刻 */
        .sidebar {
            width: 210px;
            background-color: #001529;
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 200;
            overflow-y: auto;
        }

        .sidebar .logo {
            height: 64px;
            padding: 16px 24px;
            display: flex;
            align-items: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
            border-bottom: 1px solid #1f2937;
        }

        .sidebar .logo::before {
            content: "🚗";
            margin-right: 8px;
            font-size: 24px;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: #ffffff73;
            text-decoration: none;
            transition: all 0.3s;
            cursor: pointer;
            position: relative;
        }

        .menu-item:hover,
        .menu-item.active {
            color: #1890ff;
            background-color: #111b26;
        }

        .menu-item.active::after {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background-color: #1890ff;
        }

        .menu-item .icon {
            margin-right: 12px;
            font-size: 16px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 210px;
            width: calc(100% - 210px);
            min-height: 100vh;
            background-color: #f0f2f5;
        }

        /* 顶部区域 */
        .header {
            background-color: white;
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 8px #f0f1f2;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            font-size: 14px;
        }

        .breadcrumb a {
            color: #00000073;
            text-decoration: none;
        }

        .breadcrumb a:hover {
            color: #1890ff;
        }

        .breadcrumb .current {
            color: #000000d9;
            font-weight: 500;
        }

        .breadcrumb .separator {
            margin: 0 8px;
            color: #00000040;
        }

        .user-info {
            display: flex;
            align-items: center;
            color: #00000073;
        }

        .user-info .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #1890ff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 14px;
        }

        /* 页面主体内容 */
        .page-container {
            padding: 24px;
        }

        /* 页面标题和描述区域 - 新增功能描述 */
        .page-header {
            background: white;
            padding: 24px;
            border-radius: 6px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #000000d9;
            margin-bottom: 8px;
        }

        .page-description {
            font-size: 14px;
            color: #00000073;
            margin-bottom: 0;
        }

        /* 统计概览卡片区域 - 完全新增 */
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 16px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            position: relative;
            transition: all 0.3s;
        }

        .stat-card:hover {
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }

        .stat-card .icon {
            position: absolute;
            top: 24px;
            right: 24px;
            font-size: 32px;
            opacity: 0.8;
        }

        .stat-card.total .icon {
            color: #1890ff;
        }

        .stat-card.normal .icon {
            color: #52c41a;
        }

        .stat-card.error .icon {
            color: #ff4d4f;
        }

        .stat-card.success-rate .icon {
            color: #faad14;
        }

        .stat-value {
            font-size: 30px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .stat-card.total .stat-value {
            color: #1890ff;
        }

        .stat-card.normal .stat-value {
            color: #52c41a;
        }

        .stat-card.error .stat-value {
            color: #ff4d4f;
        }

        .stat-card.success-rate .stat-value {
            color: #faad14;
        }

        .stat-label {
            font-size: 14px;
            color: #00000073;
            margin-bottom: 8px;
        }

        .stat-trend {
            display: flex;
            align-items: center;
            font-size: 12px;
        }

        .trend-up {
            color: #52c41a;
        }

        .trend-down {
            color: #ff4d4f;
        }

        .trend-icon {
            margin-right: 4px;
        }

        /* 筛选表单区域 - 功能增强 */
        .filter-form {
            background: white;
            padding: 24px;
            border-radius: 6px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 16px;
        }

        .form-row:last-child {
            margin-bottom: 0;
        }

        .form-item {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 14px;
            color: #000000d9;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-required {
            color: #ff4d4f;
            margin-right: 4px;
        }

        .form-select,
        .form-input {
            height: 36px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 0 12px;
            font-size: 14px;
            transition: all 0.3s;
            background: white;
        }

        .form-select:focus,
        .form-input:focus {
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }

        .form-select:hover,
        .form-input:hover {
            border-color: #40a9ff;
        }

        .form-checkbox {
            display: flex;
            align-items: center;
            margin-top: 28px;
        }

        .form-checkbox input {
            margin-right: 8px;
            transform: scale(1.2);
            accent-color: #1890ff;
        }

        .form-actions {
            display: flex;
            gap: 12px;
            margin-top: 16px;
        }

        .btn {
            height: 36px;
            padding: 0 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            border: 1px solid #d9d9d9;
            background: white;
            color: #000000d9;
        }

        .btn:hover {
            border-color: #40a9ff;
            color: #1890ff;
        }

        .btn-primary {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }

        /* 标签页导航 - 新增 */
        .tab-nav {
            background: white;
            border-radius: 6px 6px 0 0;
            display: flex;
            border-bottom: 1px solid #e8e8e8;
        }

        .tab-item {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
            font-size: 14px;
            color: #00000073;
        }

        .tab-item:hover {
            color: #1890ff;
        }

        .tab-item.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            background: #f0f9ff;
        }

        /* 车辆列表区域 */
        .vehicle-list-container {
            background: white;
            border-radius: 0 0 6px 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            overflow: hidden;
        }

        .list-header {
            padding: 16px 24px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .vehicle-count {
            font-size: 14px;
            color: #00000073;
        }

        .vehicle-count strong {
            color: #1890ff;
            font-weight: 600;
        }

        .list-options {
            display: flex;
            gap: 16px;
            align-items: center;
        }

        .checkbox-group {
            display: flex;
            gap: 16px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
        }

        .checkbox-item input {
            margin-right: 8px;
            transform: scale(1.2);
            accent-color: #1890ff;
        }

        /* 车辆表格 - 增强功能 */
        .vehicle-table {
            width: 100%;
            border-collapse: collapse;
        }

        .vehicle-table th,
        .vehicle-table td {
            padding: 12px 24px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
        }

        .vehicle-table th {
            background: #fafafa;
            font-weight: 500;
            color: #000000d9;
            font-size: 14px;
        }

        .vehicle-table td {
            font-size: 14px;
            color: #000000d9;
        }

        .vehicle-table tbody tr:hover {
            background: #f5f5f5;
        }

        .vehicle-info {
            display: flex;
            align-items: center;
        }

        .vehicle-icon {
            margin-right: 8px;
            color: #1890ff;
            font-size: 16px;
        }

        .vehicle-name {
            font-weight: 500;
            margin-bottom: 2px;
        }

        .vehicle-id {
            font-size: 12px;
            color: #00000073;
        }

        /* 状态标签 - 新增 */
        .status-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-normal {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .status-error {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }

        .status-warning {
            background: #fffbe6;
            color: #faad14;
            border: 1px solid #ffe58f;
        }

        /* 操作按钮 - 差异化设计 */
        .btn-action {
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
            border: 1px solid;
            text-decoration: none;
            display: inline-block;
        }

        .btn-view {
            background: #e6f7ff;
            border-color: #91d5ff;
            color: #1890ff;
        }

        .btn-view:hover {
            background: #bae7ff;
            border-color: #69c0ff;
        }

        .btn-handle {
            background: #fff2f0;
            border-color: #ffccc7;
            color: #ff4d4f;
        }

        .btn-handle:hover {
            background: #ffebe8;
            border-color: #ffa39e;
        }

        /* 日历网格区域 - 保持原样式 */
        .calendar-container {
            background: white;
            border-radius: 6px;
            margin-top: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            overflow: hidden;
        }

        .calendar-header {
            padding: 16px 24px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .calendar-title {
            font-size: 16px;
            font-weight: 500;
            color: #000000d9;
        }

        .view-selector {
            display: flex;
            gap: 8px;
        }

        .view-btn {
            padding: 4px 12px;
            border: 1px solid #d9d9d9;
            background: white;
            color: #000000d9;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .view-btn:hover {
            border-color: #40a9ff;
            color: #1890ff;
        }

        .view-btn.active {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
        }

        .calendar-day {
            border-right: 1px solid #e8e8e8;
            border-bottom: 1px solid #e8e8e8;
            min-height: 80px;
            padding: 8px;
            position: relative;
        }

        .calendar-day:nth-child(7n) {
            border-right: none;
        }

        .day-number {
            font-size: 14px;
            font-weight: 500;
            color: #000000d9;
            margin-bottom: 4px;
        }

        .day-label {
            font-size: 12px;
            color: #00000073;
            text-align: center;
            padding: 8px;
            background: #fafafa;
            border-bottom: 1px solid #e8e8e8;
        }

        /* 数据分析图表区域 - 完全新增 */
        .charts-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-top: 16px;
        }

        .chart-card {
            background: white;
            padding: 24px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 16px;
            font-weight: 500;
            color: #000000d9;
        }

        .time-selector {
            display: flex;
            gap: 8px;
        }

        .time-btn {
            padding: 4px 12px;
            border: 1px solid #d9d9d9;
            background: white;
            color: #000000d9;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 12px;
        }

        .time-btn:hover {
            border-color: #40a9ff;
            color: #1890ff;
        }

        .time-btn.active {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }

        .chart-canvas {
            width: 100%;
            max-height: 300px;
        }

        /* 操作记录区域 - 完全新增 */
        .operation-records {
            background: white;
            padding: 24px;
            border-radius: 6px;
            margin-top: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .records-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .records-title {
            font-size: 16px;
            font-weight: 500;
            color: #000000d9;
        }

        .view-all-link {
            color: #1890ff;
            text-decoration: none;
            font-size: 14px;
        }

        .view-all-link:hover {
            text-decoration: underline;
        }

        .records-table {
            width: 100%;
            border-collapse: collapse;
        }

        .records-table th,
        .records-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
            font-size: 14px;
        }

        .records-table th {
            background: #fafafa;
            font-weight: 500;
            color: #000000d9;
        }

        .records-table td {
            color: #000000d9;
        }

        .records-table tbody tr:hover {
            background: #f5f5f5;
        }

        .operation-type {
            font-weight: 500;
        }

        .operation-content {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 模态框样式 - 车辆详情 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.45);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-overlay.show {
            display: flex;
        }

        .modal {
            background: white;
            border-radius: 6px;
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 500;
            color: #000000d9;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #00000073;
            transition: color 0.3s;
        }

        .modal-close:hover {
            color: #000000d9;
        }

        .modal-body {
            padding: 24px;
            overflow-y: auto;
            flex: 1;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 24px;
        }

        .detail-section {
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 16px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 500;
            color: #000000d9;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e8e8e8;
        }

        .detail-item {
            display: flex;
            margin-bottom: 12px;
        }

        .detail-item:last-child {
            margin-bottom: 0;
        }

        .detail-label {
            width: 120px;
            color: #00000073;
            font-size: 14px;
            flex-shrink: 0;
        }

        .detail-value {
            color: #000000d9;
            font-size: 14px;
            flex: 1;
        }

        .error-reasons {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            border-radius: 6px;
            padding: 16px;
            margin-top: 16px;
        }

        .error-title {
            color: #ff4d4f;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .error-list {
            list-style: none;
            padding: 0;
        }

        .error-list li {
            color: #ff4d4f;
            font-size: 14px;
            margin-bottom: 4px;
            padding-left: 16px;
            position: relative;
        }

        .error-list li::before {
            content: "•";
            position: absolute;
            left: 0;
        }

        .suggestions {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 6px;
            padding: 16px;
            margin-top: 16px;
        }

        .suggestions-title {
            color: #52c41a;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .suggestions-list {
            list-style: none;
            padding: 0;
        }

        .suggestions-list li {
            color: #52c41a;
            font-size: 14px;
            margin-bottom: 4px;
            padding-left: 16px;
            position: relative;
        }

        .suggestions-list li::before {
            content: "✓";
            position: absolute;
            left: 0;
        }

        .modal-footer {
            padding: 12px 24px;
            border-top: 1px solid #e8e8e8;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
            }

            .charts-container {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s;
            }

            .main-content {
                margin-left: 0;
                width: 100%;
            }

            .stats-overview {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .detail-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }

        /* 工具提示 */
        .tooltip {
            position: relative;
            cursor: pointer;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s;
            z-index: 1000;
        }

        .tooltip:hover::after {
            opacity: 1;
        }
    </style>
</head>
<body>
    <!-- 整体布局容器 -->
    <div class="layout">
        <!-- 左侧导航栏 - 100%复刻SaaS系统样式 -->
        <div class="sidebar">
            <div class="logo">擎路科技</div>
            <nav class="nav-menu">
                <a href="#" class="menu-item">
                    <span class="icon">🏠</span>
                    <span>前台</span>
                </a>
                <a href="#" class="menu-item">
                    <span class="icon">📋</span>
                    <span>订单</span>
                </a>
                <a href="#" class="menu-item">
                    <span class="icon">📦</span>
                    <span>库存</span>
                </a>
                <a href="#" class="menu-item">
                    <span class="icon">💰</span>
                    <span>价格</span>
                </a>
                <a href="#" class="menu-item">
                    <span class="icon">🏪</span>
                    <span>门店</span>
                </a>
                <a href="#" class="menu-item active">
                    <span class="icon">🚗</span>
                    <span>车型</span>
                </a>
                <a href="#" class="menu-item">
                    <span class="icon">📊</span>
                    <span>数据中心</span>
                </a>
                <a href="#" class="menu-item">
                    <span class="icon">🏢</span>
                    <span>商家</span>
                </a>
                <a href="#" class="menu-item">
                    <span class="icon">⚙️</span>
                    <span>ETC管理</span>
                </a>
                <a href="#" class="menu-item">
                    <span class="icon">🎯</span>
                    <span>营销</span>
                </a>
                <a href="#" class="menu-item">
                    <span class="icon">💳</span>
                    <span>账单</span>
                </a>
                <a href="#" class="menu-item">
                    <span class="icon">🛒</span>
                    <span>采购商城</span>
                </a>
                <a href="#" class="menu-item">
                    <span class="icon">💰</span>
                    <span>财务管理</span>
                </a>
                <a href="#" class="menu-item">
                    <span class="icon">👥</span>
                    <span>用户</span>
                </a>
                <a href="#" class="menu-item">
                    <span class="icon">🔗</span>
                    <span>渠道</span>
                </a>
            </nav>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 顶部头部区域 -->
            <header class="header">
                <div class="breadcrumb">
                    <a href="#">车型</a>
                    <span class="separator">/</span>
                    <span class="current">车辆资源检测</span>
                </div>
                <div class="user-info">
                    <div class="user-avatar">企</div>
                    <span>企鹅123</span>
                    <a href="#" style="margin-left: 16px; color: #00000073; text-decoration: none;">退出</a>
                </div>
            </header>

            <!-- 页面主体内容 -->
            <div class="page-container">
                <!-- 页面标题和描述区域 - 需求变更：更新标题和新增描述 -->
                <div class="page-header fade-in">
                    <h1 class="page-title">车辆资源检测</h1>
                    <p class="page-description">快速检测您的车辆资源是否正常展示，排查展示异常问题</p>
                </div>

                <!-- 统计概览数据展示 - 完全新增功能 -->
                <div class="stats-overview fade-in">
                    <div class="stat-card total">
                        <div class="icon">🚗</div>
                        <div class="stat-value">128</div>
                        <div class="stat-label">车辆总数</div>
                        <div class="stat-trend trend-up">
                            <span class="trend-icon">↑</span>
                            <span>较上周增加 12% (14 辆)</span>
                        </div>
                    </div>
                    <div class="stat-card normal">
                        <div class="icon">✅</div>
                        <div class="stat-value">102</div>
                        <div class="stat-label">正常车辆</div>
                        <div class="stat-trend trend-up">
                            <span class="trend-icon">↑</span>
                            <span>占比 79.7%，较上周增加 8%</span>
                        </div>
                    </div>
                    <div class="stat-card error">
                        <div class="icon">⚠️</div>
                        <div class="stat-value">26</div>
                        <div class="stat-label">异常车辆</div>
                        <div class="stat-trend trend-up">
                            <span class="trend-icon">↑</span>
                            <span>占比 20.3%，较上周增加 23%</span>
                        </div>
                    </div>
                    <div class="stat-card success-rate">
                        <div class="icon">📊</div>
                        <div class="stat-value">96.2%</div>
                        <div class="stat-label">检测成功率</div>
                        <div class="stat-trend trend-down">
                            <span class="trend-icon">↓</span>
                            <span>较上周下降 1.3%</span>
                        </div>
                    </div>
                </div>

                <!-- 筛选条件区域 - 功能增强 -->
                <div class="filter-form fade-in">
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">渠道筛选</label>
                            <select class="form-select">
                                <option>全部渠道</option>
                                <option>某旅行平台</option>
                                <option>某打车平台</option>
                                <option>某租车平台</option>
                            </select>
                        </div>
                        <div class="form-item">
                            <label class="form-label"><span class="form-required">*</span>取车门店</label>
                            <select class="form-select">
                                <option>全部门店</option>
                                <option>北京首都机场店</option>
                                <option>上海虹桥机场店</option>
                                <option>广州白云机场店</option>
                            </select>
                        </div>
                        <div class="form-item">
                            <label class="form-label">还车门店</label>
                            <select class="form-select">
                                <option>全部门店</option>
                                <option>北京首都机场店</option>
                                <option>上海虹桥机场店</option>
                                <option>广州白云机场店</option>
                            </select>
                        </div>
                        <div class="form-item">
                            <label class="form-label">车型分类</label>
                            <select class="form-select">
                                <option>全部车型</option>
                                <option>经济型</option>
                                <option>舒适型</option>
                                <option>豪华型</option>
                                <option>SUV</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">取车时间</label>
                            <input type="date" class="form-input" value="2025-07-11">
                        </div>
                        <div class="form-item">
                            <label class="form-label">还车时间</label>
                            <input type="date" class="form-input" value="2025-07-25">
                        </div>
                        <div class="form-item">
                            <label class="form-label">车型搜索</label>
                            <input type="text" class="form-input" placeholder="输入车型名称或车牌号">
                        </div>
                        <div class="form-item">
                            <div class="form-checkbox">
                                <input type="checkbox" id="onlyError">
                                <label for="onlyError">仅显示异常车辆</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button class="btn btn-primary">查询</button>
                        <button class="btn">重置</button>
                        <a href="#" class="btn" style="text-decoration: none; display: inline-flex; align-items: center;">高级筛选</a>
                    </div>
                </div>

                <!-- 标签页导航 - 新增功能 -->
                <div class="tab-nav fade-in">
                    <div class="tab-item active" data-tab="all">全部车辆</div>
                    <div class="tab-item" data-tab="normal">正常车辆 (102)</div>
                    <div class="tab-item" data-tab="error">异常车辆 (26)</div>
                </div>

                <!-- 车辆列表容器 -->
                <div class="vehicle-list-container fade-in">
                    <!-- 列表头部 -->
                    <div class="list-header">
                        <div class="vehicle-count">
                            共 <strong>128</strong> 辆车
                        </div>
                        <div class="list-options">
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="orderOccupied" checked>
                                    <label for="orderOccupied">订单占用</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="nonOrderOccupied" checked>
                                    <label for="nonOrderOccupied">非订单占用</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 车辆表格 - 增强功能 -->
                    <table class="vehicle-table">
                        <thead>
                            <tr>
                                <th>车辆信息</th>
                                <th>车牌号</th>
                                <th>状态</th>
                                <th>异常原因</th>
                                <th>报文ID</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="vehicle-info">
                                        <span class="vehicle-icon">🚗</span>
                                        <div>
                                            <div class="vehicle-name">大众 迈腾GTE插电混动 2020款 GTE 豪华型</div>
                                            <div class="vehicle-id">ID: 1376</div>
                                        </div>
                                    </div>
                                </td>
                                <td>京BF09400</td>
                                <td><span class="status-tag status-normal">正常</span></td>
                                <td>-</td>
                                <td>
                                    <span class="tooltip" data-tooltip="完整ID: bf094001376xyz...">bf09400...</span>
                                </td>
                                <td>
                                    <a href="#" class="btn-action btn-view" onclick="showVehicleDetail('1376')">查看详情</a>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="vehicle-info">
                                        <span class="vehicle-icon">🚗</span>
                                        <div>
                                            <div class="vehicle-name">大众 速腾 2021款 200TSI DSG超越版</div>
                                            <div class="vehicle-id">ID: 1377</div>
                                        </div>
                                    </div>
                                </td>
                                <td>京B0CG70</td>
                                <td><span class="status-tag status-error">异常</span></td>
                                <td>价格未配置</td>
                                <td>
                                    <span class="tooltip" data-tooltip="完整ID: b0cg701377abc...">b0cg70...</span>
                                </td>
                                <td>
                                    <a href="#" class="btn-action btn-handle" onclick="showVehicleDetail('1377')">去处理</a>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="vehicle-info">
                                        <span class="vehicle-icon">🚗</span>
                                        <div>
                                            <div class="vehicle-name">本田 凌派 2019款 180Turbo CVT舒适版</div>
                                            <div class="vehicle-id">ID: 1378</div>
                                        </div>
                                    </div>
                                </td>
                                <td>京B0G082</td>
                                <td><span class="status-tag status-error">异常</span></td>
                                <td>图片缺失</td>
                                <td>
                                    <span class="tooltip" data-tooltip="完整ID: b0g0821378def...">b0g082...</span>
                                </td>
                                <td>
                                    <a href="#" class="btn-action btn-handle" onclick="showVehicleDetail('1378')">去处理</a>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="vehicle-info">
                                        <span class="vehicle-icon">🚗</span>
                                        <div>
                                            <div class="vehicle-name">丰田 卡罗拉 2021款 1.2T S-CVT GL先锋版</div>
                                            <div class="vehicle-id">ID: 1379</div>
                                        </div>
                                    </div>
                                </td>
                                <td>京BF06517</td>
                                <td><span class="status-tag status-normal">正常</span></td>
                                <td>-</td>
                                <td>
                                    <span class="tooltip" data-tooltip="完整ID: bf065171379ghi...">bf06517...</span>
                                </td>
                                <td>
                                    <a href="#" class="btn-action btn-view" onclick="showVehicleDetail('1379')">查看详情</a>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="vehicle-info">
                                        <span class="vehicle-icon">🚗</span>
                                        <div>
                                            <div class="vehicle-name">别克 君威 2020款 GS 28T 豪华型</div>
                                            <div class="vehicle-id">ID: 1380</div>
                                        </div>
                                    </div>
                                </td>
                                <td>京BF02820</td>
                                <td><span class="status-tag status-warning">部分异常</span></td>
                                <td>状态异常</td>
                                <td>
                                    <span class="tooltip" data-tooltip="完整ID: bf028201380jkl...">bf02820...</span>
                                </td>
                                <td>
                                    <a href="#" class="btn-action btn-handle" onclick="showVehicleDetail('1380')">去处理</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 数据分析图表区域 - 完全新增功能 -->
                <div class="charts-container fade-in">
                    <!-- 异常类型分布图 -->
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">异常类型分布</h3>
                            <div class="time-selector">
                                <button class="time-btn active">近7天</button>
                                <button class="time-btn">近30天</button>
                                <button class="time-btn">近90天</button>
                            </div>
                        </div>
                        <canvas id="errorTypeChart" class="chart-canvas"></canvas>
                    </div>

                    <!-- 异常趋势图 -->
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">异常趋势分析</h3>
                            <div class="time-selector">
                                <button class="time-btn active">日</button>
                                <button class="time-btn">周</button>
                                <button class="time-btn">月</button>
                            </div>
                        </div>
                        <canvas id="errorTrendChart" class="chart-canvas"></canvas>
                    </div>
                </div>

                <!-- 操作记录功能 - 完全新增 -->
                <div class="operation-records fade-in">
                    <div class="records-header">
                        <h3 class="records-title">最近操作记录</h3>
                        <a href="#" class="view-all-link">查看全部</a>
                    </div>
                    <table class="records-table">
                        <thead>
                            <tr>
                                <th>操作时间</th>
                                <th>操作类型</th>
                                <th>操作内容</th>
                                <th>操作人</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2025-01-15 14:30:25</td>
                                <td class="operation-type">修改价格</td>
                                <td class="operation-content">丰田卡罗拉日租金从 ¥180 调整为 ¥200</td>
                                <td>企鹅123</td>
                                <td><span class="status-tag status-normal">成功</span></td>
                            </tr>
                            <tr>
                                <td>2025-01-15 14:25:18</td>
                                <td class="operation-type">上传图片</td>
                                <td class="operation-content">别克君威补充车辆外观图片 (3张)</td>
                                <td>企鹅123</td>
                                <td><span class="status-tag status-normal">成功</span></td>
                            </tr>
                            <tr>
                                <td>2025-01-15 14:20:10</td>
                                <td class="operation-type">更新状态</td>
                                <td class="operation-content">丰田RAV4状态从"维修中"更新为"可用"</td>
                                <td>系统自动</td>
                                <td><span class="status-tag status-warning">部分成功</span></td>
                            </tr>
                            <tr>
                                <td>2025-01-15 14:15:45</td>
                                <td class="operation-type">批量导入</td>
                                <td class="operation-content">批量导入新车信息 (25台车辆)</td>
                                <td>企鹅123</td>
                                <td><span class="status-tag status-normal">成功</span></td>
                            </tr>
                            <tr>
                                <td>2025-01-15 14:10:30</td>
                                <td class="operation-type">系统检测</td>
                                <td class="operation-content">执行自动检测任务，发现12个异常项</td>
                                <td>系统自动</td>
                                <td><span class="status-tag status-normal">成功</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 日历视图区域 - 保持原有样式 -->
                <div class="calendar-container fade-in">
                    <div class="calendar-header">
                        <h3 class="calendar-title">7月</h3>
                        <div class="view-selector">
                            <button class="view-btn active">日</button>
                            <button class="view-btn">周</button>
                            <button class="view-btn">月</button>
                        </div>
                    </div>
                    <div class="calendar-grid">
                        <div class="day-label">11 星期五</div>
                        <div class="day-label">12 星期六</div>
                        <div class="day-label">13 星期日</div>
                        <div class="day-label">14 星期一</div>
                        <div class="day-label">15 星期二</div>
                        <div class="day-label">16 星期三</div>
                        <div class="day-label">17 星期四</div>
                        <div class="calendar-day">
                            <div class="day-number">11</div>
                        </div>
                        <div class="calendar-day">
                            <div class="day-number">12</div>
                        </div>
                        <div class="calendar-day">
                            <div class="day-number">13</div>
                        </div>
                        <div class="calendar-day">
                            <div class="day-number">14</div>
                        </div>
                        <div class="calendar-day">
                            <div class="day-number">15</div>
                        </div>
                        <div class="calendar-day">
                            <div class="day-number">16</div>
                        </div>
                        <div class="calendar-day">
                            <div class="day-number">17</div>
                        </div>
                        <div class="day-label">18 星期五</div>
                        <div class="calendar-day">
                            <div class="day-number">18</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 车辆详情模态框 - 完全新增功能 -->
    <div class="modal-overlay" id="vehicleDetailModal">
        <div class="modal">
            <div class="modal-header">
                <h2 class="modal-title">车辆详细信息</h2>
                <button class="modal-close" onclick="hideVehicleDetail()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="detail-grid">
                    <div class="detail-section">
                        <h3 class="section-title">基本信息</h3>
                        <div class="detail-item">
                            <span class="detail-label">车辆名称:</span>
                            <span class="detail-value" id="modalVehicleName">-</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">车辆ID:</span>
                            <span class="detail-value" id="modalVehicleId">-</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">车牌号:</span>
                            <span class="detail-value" id="modalLicensePlate">-</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">车型分类:</span>
                            <span class="detail-value" id="modalVehicleType">-</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">座位数:</span>
                            <span class="detail-value" id="modalSeatCount">-</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">车辆状态:</span>
                            <span class="detail-value" id="modalVehicleStatus">-</span>
                        </div>
                    </div>
                    <div class="detail-section">
                        <h3 class="section-title">业务信息</h3>
                        <div class="detail-item">
                            <span class="detail-label">车型ID:</span>
                            <span class="detail-value" id="modalModelId">-</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">取车门店:</span>
                            <span class="detail-value" id="modalPickupStore">-</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">还车门店:</span>
                            <span class="detail-value" id="modalReturnStore">-</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">报文ID:</span>
                            <span class="detail-value" id="modalMessageId">-</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">最后更新:</span>
                            <span class="detail-value" id="modalLastUpdate">-</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">创建时间:</span>
                            <span class="detail-value" id="modalCreateTime">-</span>
                        </div>
                    </div>
                </div>
                
                <!-- 异常原因详情 -->
                <div class="error-reasons" id="modalErrorReasons" style="display: none;">
                    <h4 class="error-title">🚨 检测到以下异常</h4>
                    <ul class="error-list" id="modalErrorList">
                        <!-- 动态填充异常原因 -->
                    </ul>
                </div>

                <!-- 处理建议 -->
                <div class="suggestions" id="modalSuggestions" style="display: none;">
                    <h4 class="suggestions-title">💡 处理建议</h4>
                    <ul class="suggestions-list" id="modalSuggestionsList">
                        <!-- 动态填充处理建议 -->
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="modalActionBtn">去处理</button>
                <button class="btn" onclick="hideVehicleDetail()">关闭</button>
            </div>
        </div>
    </div>

    <script>
        // =======================================================
        // JavaScript 功能实现
        // =======================================================

        // 车辆详细数据模拟
        const vehicleDetails = {
            '1376': {
                name: '大众 迈腾GTE插电混动 2020款 GTE 豪华型',
                id: '1376',
                licensePlate: '京BF09400',
                type: '豪华型',
                seatCount: '5座',
                status: '正常',
                modelId: 'VW-MAGOTAN-GTE-2020',
                pickupStore: '北京首都机场店',
                returnStore: '北京首都机场店',
                messageId: 'bf094001376xyz12345',
                lastUpdate: '2025-01-15 14:30:25',
                createTime: '2024-12-01 09:15:30',
                isError: false,
                errors: [],
                suggestions: []
            },
            '1377': {
                name: '大众 速腾 2021款 200TSI DSG超越版',
                id: '1377',
                licensePlate: '京B0CG70',
                type: '舒适型',
                seatCount: '5座',
                status: '异常',
                modelId: 'VW-SAGITAR-200TSI-2021',
                pickupStore: '北京首都机场店',
                returnStore: '北京首都机场店',
                messageId: 'b0cg701377abc67890',
                lastUpdate: '2025-01-15 10:20:15',
                createTime: '2024-11-15 16:45:20',
                isError: true,
                errors: [
                    '价格配置缺失：日租金未设置',
                    '周末价格未配置',
                    '节假日加价规则缺失'
                ],
                suggestions: [
                    '前往价格管理 → 基础价格，设置日租金',
                    '配置周末和节假日差异化定价',
                    '检查渠道价格配置是否完整'
                ]
            },
            '1378': {
                name: '本田 凌派 2019款 180Turbo CVT舒适版',
                id: '1378',
                licensePlate: '京B0G082',
                type: '经济型',
                seatCount: '5座',
                status: '异常',
                modelId: 'HONDA-CRIDER-180T-2019',
                pickupStore: '上海虹桥机场店',
                returnStore: '上海虹桥机场店',
                messageId: 'b0g0821378def13579',
                lastUpdate: '2025-01-14 18:45:30',
                createTime: '2024-10-20 11:30:45',
                isError: true,
                errors: [
                    '车辆外观图片缺失',
                    '内饰图片不完整',
                    '车辆配置图片未上传'
                ],
                suggestions: [
                    '前往车辆管理，上传外观图片（至少3张）',
                    '补充内饰图片（方向盘、座椅、仪表盘）',
                    '上传车辆配置参数图片'
                ]
            },
            '1379': {
                name: '丰田 卡罗拉 2021款 1.2T S-CVT GL先锋版',
                id: '1379',
                licensePlate: '京BF06517',
                type: '经济型',
                seatCount: '5座',
                status: '正常',
                modelId: 'TOYOTA-COROLLA-1.2T-2021',
                pickupStore: '广州白云机场店',
                returnStore: '广州白云机场店',
                messageId: 'bf065171379ghi24680',
                lastUpdate: '2025-01-15 12:15:40',
                createTime: '2024-09-10 14:20:15',
                isError: false,
                errors: [],
                suggestions: []
            },
            '1380': {
                name: '别克 君威 2020款 GS 28T 豪华型',
                id: '1380',
                licensePlate: '京BF02820',
                type: '豪华型',
                seatCount: '5座',
                status: '部分异常',
                modelId: 'BUICK-REGAL-GS-28T-2020',
                pickupStore: '北京首都机场店',
                returnStore: '北京首都机场店',
                messageId: 'bf028201380jkl97531',
                lastUpdate: '2025-01-15 09:30:20',
                createTime: '2024-08-05 10:45:55',
                isError: true,
                errors: [
                    '车辆状态同步异常',
                    '库存状态与实际不符'
                ],
                suggestions: [
                    '执行车辆状态同步操作',
                    '联系门店确认实际车辆状态',
                    '更新库存管理系统状态'
                ]
            }
        };

        // 显示车辆详情模态框
        function showVehicleDetail(vehicleId) {
            const vehicle = vehicleDetails[vehicleId];
            if (!vehicle) return;

            // 填充基本信息
            document.getElementById('modalVehicleName').textContent = vehicle.name;
            document.getElementById('modalVehicleId').textContent = vehicle.id;
            document.getElementById('modalLicensePlate').textContent = vehicle.licensePlate;
            document.getElementById('modalVehicleType').textContent = vehicle.type;
            document.getElementById('modalSeatCount').textContent = vehicle.seatCount;
            document.getElementById('modalVehicleStatus').textContent = vehicle.status;
            document.getElementById('modalModelId').textContent = vehicle.modelId;
            document.getElementById('modalPickupStore').textContent = vehicle.pickupStore;
            document.getElementById('modalReturnStore').textContent = vehicle.returnStore;
            document.getElementById('modalMessageId').textContent = vehicle.messageId;
            document.getElementById('modalLastUpdate').textContent = vehicle.lastUpdate;
            document.getElementById('modalCreateTime').textContent = vehicle.createTime;

            // 处理异常信息
            const errorSection = document.getElementById('modalErrorReasons');
            const suggestionsSection = document.getElementById('modalSuggestions');
            const actionBtn = document.getElementById('modalActionBtn');

            if (vehicle.isError) {
                // 显示异常原因
                errorSection.style.display = 'block';
                const errorList = document.getElementById('modalErrorList');
                errorList.innerHTML = '';
                vehicle.errors.forEach(error => {
                    const li = document.createElement('li');
                    li.textContent = error;
                    errorList.appendChild(li);
                });

                // 显示处理建议
                suggestionsSection.style.display = 'block';
                const suggestionsList = document.getElementById('modalSuggestionsList');
                suggestionsList.innerHTML = '';
                vehicle.suggestions.forEach(suggestion => {
                    const li = document.createElement('li');
                    li.textContent = suggestion;
                    suggestionsList.appendChild(li);
                });

                actionBtn.textContent = '去处理';
                actionBtn.className = 'btn btn-primary';
                actionBtn.style.background = '#ff4d4f';
                actionBtn.style.borderColor = '#ff4d4f';
            } else {
                // 隐藏异常信息
                errorSection.style.display = 'none';
                suggestionsSection.style.display = 'none';
                actionBtn.textContent = '编辑信息';
                actionBtn.className = 'btn btn-primary';
                actionBtn.style.background = '#1890ff';
                actionBtn.style.borderColor = '#1890ff';
            }

            // 显示模态框
            document.getElementById('vehicleDetailModal').classList.add('show');
        }

        // 隐藏车辆详情模态框
        function hideVehicleDetail() {
            document.getElementById('vehicleDetailModal').classList.remove('show');
        }

        // 点击模态框外部关闭
        document.getElementById('vehicleDetailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideVehicleDetail();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideVehicleDetail();
            }
        });

        // 标签页切换功能
        document.querySelectorAll('.tab-item').forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有active类
                document.querySelectorAll('.tab-item').forEach(t => t.classList.remove('active'));
                // 添加active类到当前标签
                this.classList.add('active');
                
                // 这里可以添加实际的筛选逻辑
                const tabType = this.getAttribute('data-tab');
                console.log('切换到标签页:', tabType);
            });
        });

        // 时间选择器功能
        document.querySelectorAll('.time-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除同组内其他按钮的active类
                const parent = this.parentElement;
                parent.querySelectorAll('.time-btn').forEach(b => b.classList.remove('active'));
                // 添加active类到当前按钮
                this.classList.add('active');
                
                console.log('时间范围切换:', this.textContent);
            });
        });

        // 视图选择器功能
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除同组内其他按钮的active类
                const parent = this.parentElement;
                parent.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                // 添加active类到当前按钮
                this.classList.add('active');
                
                console.log('视图切换:', this.textContent);
            });
        });

        // 图表初始化
        function initCharts() {
            // 异常类型分布饼图
            const errorTypeCtx = document.getElementById('errorTypeChart').getContext('2d');
            new Chart(errorTypeCtx, {
                type: 'pie',
                data: {
                    labels: ['价格异常', '信息缺失', '状态异常', '其他异常'],
                    datasets: [{
                        data: [28, 35, 18, 19],
                        backgroundColor: [
                            '#ff4d4f',
                            '#faad14',
                            '#1890ff',
                            '#52c41a'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed + '%';
                                }
                            }
                        }
                    }
                }
            });

            // 异常趋势折线图
            const errorTrendCtx = document.getElementById('errorTrendChart').getContext('2d');
            new Chart(errorTrendCtx, {
                type: 'line',
                data: {
                    labels: ['7月11日', '7月12日', '7月13日', '7月14日', '7月15日', '7月16日', '7月17日'],
                    datasets: [{
                        label: '异常车辆数',
                        data: [32, 28, 35, 29, 26, 24, 26],
                        borderColor: '#ff4d4f',
                        backgroundColor: 'rgba(255, 77, 79, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#ff4d4f',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            borderColor: '#ff4d4f',
                            borderWidth: 1
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '日期'
                            },
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '异常车辆数'
                            },
                            beginAtZero: true,
                            grid: {
                                borderDash: [2, 2]
                            }
                        }
                    }
                }
            });
        }

        // 页面加载完成后初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟初始化图表，确保DOM完全加载
            setTimeout(initCharts, 100);
            
            // 添加入场动画
            const elements = document.querySelectorAll('.fade-in');
            elements.forEach((el, index) => {
                setTimeout(() => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // 搜索功能
        function handleSearch() {
            console.log('执行搜索操作');
            // 这里可以添加实际的搜索逻辑
        }

        // 重置功能
        function handleReset() {
            console.log('重置筛选条件');
            // 这里可以添加实际的重置逻辑
        }

        // 导出功能
        function handleExport() {
            console.log('导出数据');
            // 这里可以添加实际的导出逻辑
        }

        console.log('🚗 车辆资源检测功能原型图已加载完成');
        console.log('📊 包含功能：统计概览、数据分析图表、操作记录、车辆详情等');
        console.log('✅ 基于SaaS系统100%像素级复刻，完整实现需求变更');
    </script>
</body>
</html> 